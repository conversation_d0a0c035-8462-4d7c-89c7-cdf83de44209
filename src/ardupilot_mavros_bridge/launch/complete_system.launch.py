#!/usr/bin/env python3
"""
完整系统Launch文件
启动MAVROS、桥接节点和任务控制节点，实现完整的差速车辆控制系统
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, GroupAction
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node, PushRosNamespace
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成完整系统launch描述"""
    
    # 获取包路径
    pkg_share = FindPackageShare('ardupilot_mavros_bridge')
    
    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',  # 串口连接飞控
        description='飞控连接URL - 串口'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',  # UDP广播给GCS
        description='地面站连接URL - UDP广播'
    )
    
    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='系统命名空间'
    )
    
    declare_mavros_namespace = DeclareLaunchArgument(
        'mavros_namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )
    
    declare_vehicle_type = DeclareLaunchArgument(
        'vehicle_type',
        default_value='rover',
        description='车辆类型: rover(地面车) 或 boat(船只)'
    )
    
    # 差速驱动参数
    declare_left_motor_channel = DeclareLaunchArgument(
        'left_motor_channel',
        default_value='1',
        description='左电机通道号'
    )

    declare_right_motor_channel = DeclareLaunchArgument(
        'right_motor_channel',
        default_value='3',
        description='右电机通道号'
    )

    declare_wheelbase = DeclareLaunchArgument(
        'wheelbase',
        default_value='0.4',
        description='车辆轮距 (m)'
    )
    
    declare_max_linear_speed = DeclareLaunchArgument(
        'max_linear_speed',
        default_value='2.0',
        description='最大线速度 (m/s)'
    )
    
    declare_max_angular_speed = DeclareLaunchArgument(
        'max_angular_speed',
        default_value='2.0', 
        description='最大角速度 (rad/s)'
    )
    
    # 功能开关
    declare_enable_bridge = DeclareLaunchArgument(
        'enable_bridge',
        default_value='true',
        description='启用RC到cmd_vel桥接'
    )
    
    declare_enable_mission_control = DeclareLaunchArgument(
        'enable_mission_control',
        default_value='true',
        description='启用任务控制'
    )
    
    declare_auto_mission_start = DeclareLaunchArgument(
        'auto_mission_start',
        default_value='false',
        description='自动开始任务'
    )
    
    # 日志级别
    declare_log_level = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='日志级别'
    )
    
    # MAVROS节点
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',
        namespace=LaunchConfiguration('mavros_namespace'),
        output='screen',
        parameters=[
            PathJoinSubstitution([pkg_share, 'config', 'apm_config.yaml']),
            PathJoinSubstitution([pkg_share, 'config', 'apm_pluginlists.yaml']),
            {
                'fcu_url': LaunchConfiguration('fcu_url'),
                'gcs_url': LaunchConfiguration('gcs_url'), 
                'target_system_id': 1,
                'target_component_id': 1,
                'fcu_protocol': 'v2.0',
            }
        ],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    # MAVROS桥接节点
    bridge_node = Node(
        package='ardupilot_mavros_bridge',
        executable='mavros_bridge_node',
        name='mavros_bridge',
        output='screen',
        parameters=[{
            'left_motor_channel': LaunchConfiguration('left_motor_channel'),
            'right_motor_channel': LaunchConfiguration('right_motor_channel'),
            'wheelbase': LaunchConfiguration('wheelbase'),
            'wheel_radius': 0.1,
            'max_motor_speed': LaunchConfiguration('max_linear_speed'),
            'max_linear_speed': LaunchConfiguration('max_linear_speed'),
            'max_angular_speed': LaunchConfiguration('max_angular_speed'),
            'cmd_vel_topic': '/cmd_vel',
            'rc_out_topic': '/mavros/rc/out',
            'enable_safety_check': True,
            'timeout_duration': 1.0,
        }],
        condition=IfCondition(LaunchConfiguration('enable_bridge')),
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    # 任务控制节点
    mission_control_node = Node(
        package='ardupilot_mavros_bridge',
        executable='mission_control_node',
        name='mission_control',
        output='screen',
        parameters=[{
            'mavros_namespace': '/mavros',
            'auto_mission_start': LaunchConfiguration('auto_mission_start'),
            'mission_timeout': 300.0,
            'waypoint_tolerance': 2.0,
            'max_speed': LaunchConfiguration('max_linear_speed'),
            'mission_path_topic': '/mission_path',
            'target_pose_topic': '/target_pose',
            'global_frame_id': 'map',
            'local_frame_id': 'base_link',
        }],
        condition=IfCondition(LaunchConfiguration('enable_mission_control')),
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    # 创建带命名空间的组
    system_group = GroupAction([
        PushRosNamespace(LaunchConfiguration('namespace')),
        mavros_node,
        bridge_node,
        mission_control_node,
    ])
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加launch参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_namespace)
    ld.add_action(declare_mavros_namespace)
    ld.add_action(declare_vehicle_type)
    ld.add_action(declare_left_motor_channel)
    ld.add_action(declare_right_motor_channel)
    ld.add_action(declare_wheelbase)
    ld.add_action(declare_max_linear_speed)
    ld.add_action(declare_max_angular_speed)
    ld.add_action(declare_enable_bridge)
    ld.add_action(declare_enable_mission_control)
    ld.add_action(declare_auto_mission_start)
    ld.add_action(declare_log_level)
    
    # 添加节点组
    ld.add_action(system_group)
    
    return ld
