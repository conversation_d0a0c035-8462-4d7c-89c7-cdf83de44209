{"artifacts": [{"path": "rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "ament_target_dependencies", "add_dependencies", "set_target_properties", "set_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 226, "parent": 4}, {"command": 4, "file": 0, "line": 282, "parent": 4}, {"command": 5, "file": 0, "line": 246, "parent": 4}, {"command": 5, "file": 0, "line": 260, "parent": 4}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 9}, {"command": 6, "file": 0, "line": 276, "parent": 4}, {"command": 5, "file": 4, "line": 151, "parent": 11}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 13}, {"command": 6, "file": 0, "line": 262, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 15}, {"command": 7, "file": 0, "line": 273, "parent": 4}, {"command": 7, "file": 0, "line": 229, "parent": 4}, {"command": 9, "file": 0, "line": 239, "parent": 4}, {"command": 8, "file": 0, "line": 146, "parent": 19}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 7, "parent": 21}, {"command": 11, "file": 7, "line": 21, "parent": 22}, {"file": 6, "parent": 23}, {"command": 1, "file": 6, "line": 41, "parent": 24}, {"file": 5, "parent": 25}, {"command": 10, "file": 5, "line": 25, "parent": 26}, {"command": 12, "file": 0, "line": 253, "parent": 4}, {"command": 12, "file": 4, "line": 141, "parent": 15}, {"command": 12, "file": 4, "line": 141, "parent": 13}, {"command": 12, "file": 4, "line": 141, "parent": 9}, {"command": 12, "file": 4, "line": 147, "parent": 11}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 20, "fragment": "-Wall"}, {"backtrace": 20, "fragment": "-Wextra"}, {"fragment": "-std=gnu99"}], "defines": [{"backtrace": 7, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 27, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}, {"define": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext_EXPORTS"}], "includes": [{"backtrace": 28, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 28, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py"}, {"backtrace": 28, "path": "/usr/include/python3.10"}, {"backtrace": 7, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 30, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 31, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 32, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}], "language": "C", "languageStandard": {"backtraces": [5], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 17, "id": "rslidar_msg__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df"}, {"backtrace": 18, "id": "rslidar_msg__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 7, "id": "rslidar_msg__rosidl_generator_py::@6890427a1f51a3e7e1df"}, {"backtrace": 18, "id": "rslidar_msg__py::@c80b5731effdf274c015"}], "id": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "local/lib/python3.10/dist-packages/rslidar_msg"}, {"backtrace": 6, "path": "local/lib/python3.10/dist-packages/rslidar_msg"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py/rslidar_msg:/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "rosidl_generator_py/rslidar_msg/librslidar_msg__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 7, "fragment": "librslidar_msg__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "librslidar_msg__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "C"}, "name": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext", "nameOnDisk": "rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}