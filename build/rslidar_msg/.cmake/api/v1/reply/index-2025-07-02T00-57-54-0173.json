{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-96d6cd188a52b39657b5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-96d6cd188a52b39657b5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}