[0.005s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.043s] -- The C compiler identification is GNU 11.4.0
[0.077s] -- The CXX compiler identification is GNU 11.4.0
[0.082s] -- Detecting C compiler ABI info
[0.128s] -- Detecting C compiler ABI info - done
[0.133s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.133s] -- Detecting C compile features
[0.134s] -- Detecting C compile features - done
[0.135s] -- Detecting CXX compiler ABI info
[0.185s] -- Detecting CXX compiler ABI info - done
[0.190s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.190s] -- Detecting CXX compile features
[0.190s] -- Detecting CXX compile features - done
[0.190s] [0m=============================================================[0m
[0.190s] [0m-- POINT_TYPE is XYZI[0m
[0.191s] [0m=============================================================[0m
[0.195s] [0m=============================================================[0m
[0.195s] [0m-- ROS Not Found. ROS Support is turned Off.[0m
[0.195s] [0m=============================================================[0m
[0.281s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.347s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.350s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.353s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.359s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.367s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.388s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.389s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.442s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.455s] -- Found FastRTPS: /opt/ros/humble/include  
[0.475s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.479s] -- Looking for pthread.h
[0.524s] -- Looking for pthread.h - found
[0.524s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.572s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.573s] -- Found Threads: TRUE  
[0.599s] [0m=============================================================[0m
[0.599s] [0m-- ROS2 Found. ROS2 Support is turned On.[0m
[0.599s] [0m=============================================================[0m
[0.600s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.618s] -- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)
[0.627s] [0m=============================================================[0m
[0.627s] [0m-- CMake run for UNIX GNU Compiler[0m
[0.627s] [0m=============================================================[0m
[0.627s] [0m=============================================================[0m
[0.627s] [0m-- rs_driver Version : v1.5.17[0m
[0.627s] [0m=============================================================[0m
[0.666s] -- Configuring done
[0.671s] -- Generating done
[0.673s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk
[0.680s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.680s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.716s] [ 33%] [32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.o[0m
[0.717s] [ 66%] [32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.o[0m
[11.931s] [100%] [32m[1mLinking CXX executable rslidar_sdk_node[0m
[12.067s] [100%] Built target rslidar_sdk_node
[12.076s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[12.078s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[12.084s] -- Install configuration: "Release"
[12.084s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
[12.084s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node" to ""
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
[12.085s] -- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
[12.086s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
