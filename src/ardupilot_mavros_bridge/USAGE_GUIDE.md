# ArduPilot MAVROS Bridge 使用指南

## 系统概述

本功能包专为差速无人车/无人船设计，实现了以下核心功能：

1. **RC控制桥接**: 将ArduPilot的RC输出通道转换为ROS标准的cmd_vel话题
2. **任务控制**: 通过MAVROS发送目标点控制车辆按预定轨迹行驶
3. **航线管理**: 获取和监控GCS发送给ArduPilot的航线信息
4. **系统监控**: 实时监控飞控状态和位置信息

## 分层导航架构设计

### 系统架构概述
```
GCS (Mission Planner) → 全局航线规划
    ↓
MAVROS → 航线接收和状态监控
    ↓
Navigation2 → 局部路径规划和避障
    ↓
ArduPilot (AUTO模式) → 底层运动控制
    ↓
差速车辆 → 执行运动
```

### 控制层级说明

1. **全局层 (GCS)**:
   - 使用Mission Planner等GCS软件规划全局航线
   - 定义任务waypoint和飞行模式
   - 监控任务执行状态

2. **规划层 (Navigation2)**:
   - 接收GCS规划的全局路径作为参考
   - 实现局部路径规划和动态避障
   - 生成安全的局部waypoint序列
   - 处理传感器数据进行环境感知

3. **执行层 (ArduPilot)**:
   - AUTO模式下执行waypoint跟随
   - 底层车辆运动控制和稳定
   - 提供精确的位置和姿态控制
   - 安全保护和故障处理

### 避障集成方案

**设计原则**:
- 避障系统作为上层路径规划，不参与底层控制
- 通过waypoint接口与ArduPilot通信
- 保持ArduPilot的底层控制权威性

**实现方式**:
1. **检测AUTO模式**: 监控ArduPilot进入AUTO模式
2. **读取全局路径**: 自动获取GCS规划的航线
3. **局部规划**: Navigation2基于全局路径进行局部避障规划
4. **waypoint更新**: 将避障后的路径转换为waypoint发送给ArduPilot

## 硬件连接

### 飞控连接
- **串口**: `/dev/ttyACM0`
- **波特率**: `921600`
- **协议**: MAVLink v2.0

### GCS通信
- **方式**: UDP广播
- **端口**: `14550`
- **地址**: 广播到所有网络接口

## 快速开始

### 1. 基本测试（推荐）

使用简化配置进行基本功能测试：

```bash
# 启动基本系统（MAVROS + RC桥接）
ros2 launch ardupilot_mavros_bridge simple_test.launch.py

# 如果需要修改连接参数
ros2 launch ardupilot_mavros_bridge simple_test.launch.py \
    fcu_url:=/dev/ttyACM0:921600 \
    gcs_url:=udp-b://@14550
```

### 2. 完整系统启动

启动所有功能（MAVROS + 桥接 + 任务控制）：

```bash
ros2 launch ardupilot_mavros_bridge complete_system.launch.py
```

### 3. 仅启动MAVROS

如果只需要MAVROS连接：

```bash
ros2 launch ardupilot_mavros_bridge apm.launch.py
```

## 功能验证

### 检查连接状态

```bash
# 查看MAVROS状态
ros2 topic echo /mavros/state

# 查看RC输出
ros2 topic echo /mavros/rc/out

# 查看转换后的cmd_vel
ros2 topic echo /cmd_vel
```

### 检查航线信息

```bash
# 查看当前航线
ros2 topic echo /mavros/mission/waypoints

# 查看任务路径（可视化用）
ros2 topic echo /mission_path
```

### 检查位置信息

```bash
# GPS位置
ros2 topic echo /mavros/global_position/global

# 本地位置
ros2 topic echo /mavros/local_position/pose

# IMU数据
ros2 topic echo /mavros/imu/data
```

## 参数配置

### RC通道映射

根据您的ArduPilot配置调整：

```bash
# 启动时指定通道
ros2 launch ardupilot_mavros_bridge simple_test.launch.py \
    throttle_channel:=1 \
    steering_channel:=3
```

### 速度限制

```bash
# 设置最大速度
ros2 launch ardupilot_mavros_bridge simple_test.launch.py \
    max_linear_speed:=1.5 \
    max_angular_speed:=1.0
```

## ArduPilot配置建议

### 基本参数

```
# 车辆类型
FRAME_CLASS = 1        # Rover
FRAME_TYPE = 1         # Skid Steering

# 伺服输出功能
SERVO1_FUNCTION = 73   # ThrottleLeft
SERVO3_FUNCTION = 74   # ThrottleRight

# 串口配置
SERIAL0_PROTOCOL = 2   # MAVLink2
SERIAL0_BAUD = 921     # 921600 baud
```

### 任务相关参数

```
# 航点导航
WP_RADIUS = 2.0        # 航点半径(米)
WP_SPEED = 2.0         # 航点速度(m/s)

# 模式设置
MODE1 = 0              # Manual
MODE2 = 3              # Steering  
MODE3 = 10             # Auto
MODE4 = 15             # Guided
```

## 故障排除

### 1. 无法连接飞控

**症状**: MAVROS显示连接失败
**解决方案**:
- 检查串口设备是否存在: `ls -l /dev/ttyACM*`
- 检查串口权限: `sudo chmod 666 /dev/ttyACM0`
- 确认波特率设置正确
- 检查ArduPilot的SERIAL0_PROTOCOL参数

### 2. 没有RC输出数据

**症状**: `/mavros/rc/out`话题无数据
**解决方案**:
- 确认ArduPilot已解锁
- 检查SERVO功能配置
- 确认RC接收机正常工作
- 检查ArduPilot模式（需要在Manual或其他输出模式）

### 3. cmd_vel数据异常

**症状**: 速度命令不正确
**解决方案**:
- 检查PWM范围配置
- 调整死区参数
- 确认通道映射正确
- 检查最大速度限制

### 4. 航线无法获取

**症状**: 无法从GCS获取航线
**解决方案**:
- 确认GCS已连接到ArduPilot
- 检查网络连接
- 手动拉取航线: `ros2 service call /mavros/mission/pull mavros_msgs/srv/WaypointPull`

## Navigation2集成指南

### 系统依赖

```bash
# 安装Navigation2
sudo apt install ros-humble-navigation2 ros-humble-nav2-bringup

# 安装传感器驱动 (根据实际硬件选择)
sudo apt install ros-humble-rplidar-ros  # 激光雷达
sudo apt install ros-humble-realsense2-camera  # 深度相机
```

### 集成步骤

1. **配置Navigation2参数**:
   ```yaml
   # nav2_params.yaml
   bt_navigator:
     ros__parameters:
       global_frame: map
       robot_base_frame: base_link

   controller_server:
     ros__parameters:
       controller_frequency: 20.0

   planner_server:
     ros__parameters:
       planner_plugins: ["GridBased"]
   ```

2. **创建避障节点**:
   ```python
   # 监控AUTO模式，触发Navigation2规划
   def auto_mode_callback(self, state_msg):
       if state_msg.mode == "AUTO" and not self.nav2_active:
           self.start_navigation2_planning()

   # 将Navigation2路径转换为ArduPilot waypoint
   def path_to_waypoints(self, nav2_path):
       waypoints = []
       for pose in nav2_path.poses:
           wp = Waypoint()
           wp.x_lat = pose.pose.position.x
           wp.y_long = pose.pose.position.y
           waypoints.append(wp)
       return waypoints
   ```

3. **启动完整导航系统**:
   ```bash
   # 启动基础系统
   ros2 launch ardupilot_mavros_bridge complete_system.launch.py

   # 启动Navigation2
   ros2 launch nav2_bringup navigation_launch.py

   # 启动传感器
   ros2 launch rplidar_ros rplidar.launch.py
   ```

### 避障工作流程

1. **任务初始化**:
   - GCS规划全局航线并上传到ArduPilot
   - 系统监控ArduPilot状态，等待AUTO模式

2. **避障激活**:
   - 检测到AUTO模式后，读取全局航线
   - 启动Navigation2局部规划器
   - 开始实时避障路径规划

3. **路径执行**:
   - Navigation2生成避障路径
   - 转换为ArduPilot waypoint格式
   - 动态更新ArduPilot的航线

4. **安全监控**:
   - 持续监控传感器数据
   - 检测障碍物和路径偏差
   - 必要时触发紧急停止

## 高级功能

### 发送目标点

```bash
# 发送全局目标点（GPS坐标）
ros2 topic pub /mavros/setpoint_raw/global mavros_msgs/msg/GlobalPositionTarget "
header:
  stamp: {sec: 0, nanosec: 0}
  frame_id: 'map'
coordinate_frame: 6
type_mask: 4088
latitude: 31.123456
longitude: 121.123456
altitude: 0.0
"
```

### 切换飞行模式

```bash
# 切换到引导模式
ros2 service call /mavros/set_mode mavros_msgs/srv/SetMode "
base_mode: 0
custom_mode: 'GUIDED'
"

# 切换到自动模式
ros2 service call /mavros/set_mode mavros_msgs/srv/SetMode "
base_mode: 0
custom_mode: 'AUTO'
"
```

### 解锁/上锁

```bash
# 解锁
ros2 service call /mavros/cmd/arming mavros_msgs/srv/CommandBool "value: true"

# 上锁
ros2 service call /mavros/cmd/arming mavros_msgs/srv/CommandBool "value: false"
```

## 可视化

使用RViz查看车辆状态和航线：

```bash
# 启动RViz
rviz2

# 添加以下话题进行可视化：
# - /mavros/local_position/pose (PoseStamped)
# - /mission_path (Path)
# - /target_pose (PoseStamped)
```

## 日志和调试

```bash
# 查看详细日志
ros2 launch ardupilot_mavros_bridge simple_test.launch.py \
    --ros-args --log-level debug

# 查看特定节点日志
ros2 node info /mavros_bridge
ros2 node info /mission_control
```

## 技术考虑和最佳实践

### 分层架构优势

1. **模块化设计**: 各层职责清晰，便于开发和维护
2. **技术成熟**: 利用现有成熟框架，降低开发风险
3. **灵活扩展**: 可以独立升级各层功能
4. **故障隔离**: 单层故障不会影响整个系统

### 实现要点

1. **时序协调**:
   - Navigation2规划频率与ArduPilot waypoint更新频率匹配
   - 避免频繁更新waypoint造成系统不稳定

2. **坐标系统一**:
   - 确保GCS、Navigation2、ArduPilot使用一致的坐标系
   - 正确处理GPS坐标与本地坐标的转换

3. **安全机制**:
   - 设置避障失败时的安全策略
   - 保留ArduPilot的紧急停止权限
   - 实现超时和故障检测机制

4. **性能优化**:
   - 合理设置传感器数据处理频率
   - 优化路径规划算法参数
   - 平衡避障精度与计算效率

### 传感器配置建议

1. **激光雷达**: 用于2D平面避障，适合结构化环境
2. **深度相机**: 提供3D环境信息，适合复杂地形
3. **IMU融合**: 结合ArduPilot IMU提高定位精度
4. **GPS RTK**: 提供高精度全局定位

## 注意事项

1. **安全第一**: 始终在安全环境中测试，确保车辆有足够的活动空间
2. **权限设置**: 确保用户有访问串口设备的权限
3. **网络配置**: 确保GCS和车载计算机在同一网络中
4. **参数备份**: 修改ArduPilot参数前请备份原始配置
5. **版本兼容**: 确保MAVROS版本与ArduPilot版本兼容
6. **避障测试**: 在启用避障前，先在安全环境中充分测试
7. **传感器校准**: 确保所有传感器正确校准和配置

## 技术支持

如遇问题，请检查：
1. ROS2日志输出
2. ArduPilot日志文件
3. 网络连接状态
4. 硬件连接情况
