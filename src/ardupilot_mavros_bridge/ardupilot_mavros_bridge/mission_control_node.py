#!/usr/bin/env python3
"""
任务控制节点
用于发送目标点控制无人车/船按照预定轨迹行驶，并获取GCS发送的航线信息
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy

import math
from typing import List, Optional

# MAVROS消息类型
from mavros_msgs.msg import (
    WaypointList, Waypoint, State, GlobalPositionTarget,
    PositionTarget, CommandCode
)
from mavros_msgs.srv import (
    WaypointPull, WaypointPush, WaypointClear, 
    CommandBool, CommandTOL, SetMode
)

# 标准ROS消息类型
from geometry_msgs.msg import PoseStamped, Point, Quaternion
from geographic_msgs.msg import GeoPoint
from std_msgs.msg import Header
from nav_msgs.msg import Path
from sensor_msgs.msg import NavSatFix


class MissionControlNode(Node):
    """任务控制节点"""
    
    def __init__(self):
        super().__init__('mission_control_node')
        
        # 声明参数
        self.declare_parameters(
            namespace='',
            parameters=[
                # 控制参数
                ('auto_mission_start', False),    # 自动开始任务
                ('mission_timeout', 300.0),       # 任务超时时间(秒)
                ('waypoint_tolerance', 2.0),      # 航点容差(米)
                ('max_speed', 2.0),               # 最大速度(m/s)
                
                # 话题名称
                ('mavros_namespace', '/mavros'),
                ('mission_path_topic', '/mission_path'),
                ('target_pose_topic', '/target_pose'),
                
                # 坐标系
                ('global_frame_id', 'map'),
                ('local_frame_id', 'base_link'),
            ]
        )
        
        # 获取参数
        self.auto_mission_start = self.get_parameter('auto_mission_start').value
        self.mission_timeout = self.get_parameter('mission_timeout').value
        self.waypoint_tolerance = self.get_parameter('waypoint_tolerance').value
        self.max_speed = self.get_parameter('max_speed').value
        
        self.mavros_ns = self.get_parameter('mavros_namespace').value
        self.mission_path_topic = self.get_parameter('mission_path_topic').value
        self.target_pose_topic = self.get_parameter('target_pose_topic').value
        
        self.global_frame_id = self.get_parameter('global_frame_id').value
        self.local_frame_id = self.get_parameter('local_frame_id').value
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # 订阅者
        self.state_subscriber = self.create_subscription(
            State,
            f'{self.mavros_ns}/state',
            self.state_callback,
            qos_profile
        )
        
        self.waypoint_subscriber = self.create_subscription(
            WaypointList,
            f'{self.mavros_ns}/mission/waypoints',
            self.waypoint_callback,
            qos_profile
        )
        
        self.global_position_subscriber = self.create_subscription(
            NavSatFix,
            f'{self.mavros_ns}/global_position/global',
            self.global_position_callback,
            qos_profile
        )
        
        self.local_position_subscriber = self.create_subscription(
            PoseStamped,
            f'{self.mavros_ns}/local_position/pose',
            self.local_position_callback,
            qos_profile
        )
        
        # 发布者
        self.mission_path_publisher = self.create_publisher(
            Path,
            self.mission_path_topic,
            10
        )
        
        self.setpoint_global_publisher = self.create_publisher(
            GlobalPositionTarget,
            f'{self.mavros_ns}/setpoint_raw/global',
            10
        )
        
        self.setpoint_local_publisher = self.create_publisher(
            PoseStamped,
            f'{self.mavros_ns}/setpoint_position/local',
            10
        )
        
        self.target_pose_publisher = self.create_publisher(
            PoseStamped,
            self.target_pose_topic,
            10
        )
        
        # 服务客户端
        self.waypoint_pull_client = self.create_client(
            WaypointPull, f'{self.mavros_ns}/mission/pull'
        )
        self.waypoint_push_client = self.create_client(
            WaypointPush, f'{self.mavros_ns}/mission/push'
        )
        self.waypoint_clear_client = self.create_client(
            WaypointClear, f'{self.mavros_ns}/mission/clear'
        )
        self.arming_client = self.create_client(
            CommandBool, f'{self.mavros_ns}/cmd/arming'
        )
        self.set_mode_client = self.create_client(
            SetMode, f'{self.mavros_ns}/set_mode'
        )
        
        # 状态变量
        self.current_state = State()
        self.current_waypoints: List[Waypoint] = []
        self.current_global_position: Optional[NavSatFix] = None
        self.current_local_position: Optional[PoseStamped] = None
        self.mission_active = False
        self.current_waypoint_index = 0
        
        # 定时器
        self.mission_timer = self.create_timer(1.0, self.mission_timer_callback)
        self.status_timer = self.create_timer(5.0, self.status_timer_callback)
        
        self.get_logger().info('任务控制节点已启动')
        self.get_logger().info(f'MAVROS命名空间: {self.mavros_ns}')
        
        # 初始化时拉取航线
        self.create_timer(2.0, self.initial_waypoint_pull)
    
    def state_callback(self, msg: State):
        """飞控状态回调"""
        self.current_state = msg
        
        # 检查模式变化
        if msg.mode == "AUTO" and not self.mission_active:
            self.mission_active = True
            self.get_logger().info('任务模式激活')
        elif msg.mode != "AUTO" and self.mission_active:
            self.mission_active = False
            self.get_logger().info('任务模式停用')
    
    def waypoint_callback(self, msg: WaypointList):
        """航点列表回调"""
        self.current_waypoints = msg.waypoints
        self.get_logger().info(f'收到航线，包含 {len(self.current_waypoints)} 个航点')
        
        # 发布航线路径用于可视化
        self.publish_mission_path()
    
    def global_position_callback(self, msg: NavSatFix):
        """全局位置回调"""
        self.current_global_position = msg
    
    def local_position_callback(self, msg: PoseStamped):
        """本地位置回调"""
        self.current_local_position = msg
    
    def publish_mission_path(self):
        """发布任务路径用于可视化"""
        if not self.current_waypoints:
            return
        
        path_msg = Path()
        path_msg.header.stamp = self.get_clock().now().to_msg()
        path_msg.header.frame_id = self.global_frame_id
        
        for waypoint in self.current_waypoints:
            if waypoint.command == CommandCode.NAV_WAYPOINT:
                pose = PoseStamped()
                pose.header = path_msg.header
                pose.pose.position.x = waypoint.x_lat
                pose.pose.position.y = waypoint.y_long
                pose.pose.position.z = waypoint.z_alt
                
                # 简单的航向计算
                pose.pose.orientation.w = 1.0
                
                path_msg.poses.append(pose)
        
        self.mission_path_publisher.publish(path_msg)
    
    def send_global_setpoint(self, lat: float, lon: float, alt: float = 0.0):
        """发送全局设定点"""
        target = GlobalPositionTarget()
        target.header.stamp = self.get_clock().now().to_msg()
        target.header.frame_id = self.global_frame_id
        target.coordinate_frame = GlobalPositionTarget.FRAME_GLOBAL_REL_ALT
        target.type_mask = (
            GlobalPositionTarget.IGNORE_VX |
            GlobalPositionTarget.IGNORE_VY |
            GlobalPositionTarget.IGNORE_VZ |
            GlobalPositionTarget.IGNORE_AFX |
            GlobalPositionTarget.IGNORE_AFY |
            GlobalPositionTarget.IGNORE_AFZ |
            GlobalPositionTarget.IGNORE_YAW_RATE
        )
        
        target.latitude = lat
        target.longitude = lon
        target.altitude = alt
        
        self.setpoint_global_publisher.publish(target)
        
        # 同时发布目标位姿用于可视化
        pose_msg = PoseStamped()
        pose_msg.header = target.header
        pose_msg.pose.position.x = lat
        pose_msg.pose.position.y = lon
        pose_msg.pose.position.z = alt
        pose_msg.pose.orientation.w = 1.0
        
        self.target_pose_publisher.publish(pose_msg)
    
    def mission_timer_callback(self):
        """任务定时器回调"""
        if not self.mission_active or not self.current_waypoints:
            return
        
        # 这里可以添加任务执行逻辑
        # 例如：检查是否到达当前航点，切换到下一个航点等
        pass
    
    def status_timer_callback(self):
        """状态定时器回调"""
        self.get_logger().info(
            f'状态: 连接={self.current_state.connected}, '
            f'解锁={self.current_state.armed}, '
            f'模式={self.current_state.mode}, '
            f'航点数={len(self.current_waypoints)}'
        )
    
    def initial_waypoint_pull(self):
        """初始航点拉取"""
        if self.waypoint_pull_client.wait_for_service(timeout_sec=5.0):
            request = WaypointPull.Request()
            future = self.waypoint_pull_client.call_async(request)
            future.add_done_callback(self.waypoint_pull_callback)
        else:
            self.get_logger().warn('航点拉取服务不可用')
    
    def waypoint_pull_callback(self, future):
        """航点拉取回调"""
        try:
            response = future.result()
            if response.success:
                self.get_logger().info(f'成功拉取 {response.wp_received} 个航点')
            else:
                self.get_logger().warn('航点拉取失败')
        except Exception as e:
            self.get_logger().error(f'航点拉取异常: {str(e)}')


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        node = MissionControlNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'节点运行出错: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
