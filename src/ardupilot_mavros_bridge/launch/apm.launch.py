#!/usr/bin/env python3
"""
ArduPilot MAVROS Launch文件 - ROS2版本
专为差速无人车/无人船优化的MAVROS启动配置
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成ArduPilot MAVROS launch描述"""

    # 获取包路径
    pkg_share = FindPackageShare('ardupilot_mavros_bridge')
    mavros_share = FindPackageShare('mavros')

    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',  # 串口连接飞控
        description='飞控连接URL - 串口'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',  # UDP广播给GCS
        description='地面站连接URL - UDP广播'
    )

    declare_tgt_system = DeclareLaunchArgument(
        'tgt_system',
        default_value='1',
        description='目标系统ID'
    )

    declare_tgt_component = DeclareLaunchArgument(
        'tgt_component',
        default_value='1',
        description='目标组件ID'
    )

    declare_fcu_protocol = DeclareLaunchArgument(
        'fcu_protocol',
        default_value='v2.0',
        description='MAVLink协议版本'
    )

    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )

    declare_log_output = DeclareLaunchArgument(
        'log_output',
        default_value='screen',
        description='日志输出方式'
    )

    declare_respawn_mavros = DeclareLaunchArgument(
        'respawn_mavros',
        default_value='false',
        description='是否自动重启MAVROS'
    )

    # MAVROS节点
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',
        namespace=LaunchConfiguration('namespace'),
        output=LaunchConfiguration('log_output'),
        parameters=[{
            'fcu_url': LaunchConfiguration('fcu_url'),
            'gcs_url': LaunchConfiguration('gcs_url'),
            'target_system_id': LaunchConfiguration('tgt_system'),
            'target_component_id': LaunchConfiguration('tgt_component'),
            'fcu_protocol': LaunchConfiguration('fcu_protocol'),
        }],
        respawn=LaunchConfiguration('respawn_mavros'),
        arguments=['--ros-args', '--log-level', 'info']
    )

    # 创建launch描述
    ld = LaunchDescription()

    # 添加launch参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_tgt_system)
    ld.add_action(declare_tgt_component)
    ld.add_action(declare_fcu_protocol)
    ld.add_action(declare_namespace)
    ld.add_action(declare_log_output)
    ld.add_action(declare_respawn_mavros)

    # 添加节点
    ld.add_action(mavros_node)

    return ld
