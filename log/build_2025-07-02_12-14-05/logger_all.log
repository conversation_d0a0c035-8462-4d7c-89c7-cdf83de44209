[0.067s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.068s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x75800ebfad70>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x75800edd03d0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x75800edd03d0>>)
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.165s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.199s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 2 installed packages in /home/<USER>/ros_ws/install
[0.200s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 283 installed packages in /opt/ros/humble
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.219s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': False, 'test_result_base': None}
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.219s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.219s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': False, 'test_result_base': None}
[0.219s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.220s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.220s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.220s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.222s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.222s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.222s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.228s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.448s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.488s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.488s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.491s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.491s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.492s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.493s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.493s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.494s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.494s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.494s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.496s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.496s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.496s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.497s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.497s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.498s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.499s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.499s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[0.499s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[0.499s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.499s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.506s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.571s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.572s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.572s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.572s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.573s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.574s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.574s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.574s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.574s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.574s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.574s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.575s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.575s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.575s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.575s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.576s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.577s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.577s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.577s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.577s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.577s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.577s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.577s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.580s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.580s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.580s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.587s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.588s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[0.588s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[0.589s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[0.590s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[0.591s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[0.591s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[0.592s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[0.593s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[0.594s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[0.594s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
