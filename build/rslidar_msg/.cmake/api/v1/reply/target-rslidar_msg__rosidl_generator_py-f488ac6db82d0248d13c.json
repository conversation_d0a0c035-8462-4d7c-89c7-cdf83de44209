{"artifacts": [{"path": "rosidl_generator_py/rslidar_msg/librslidar_msg__rosidl_generator_py.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "add_dependencies", "set_target_properties", "set_lib_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 166, "parent": 4}, {"command": 4, "file": 0, "line": 302, "parent": 4}, {"command": 5, "file": 0, "line": 175, "parent": 4}, {"command": 5, "file": 0, "line": 213, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 6, "file": 0, "line": 169, "parent": 4}, {"command": 8, "file": 0, "line": 294, "parent": 4}, {"command": 7, "file": 0, "line": 156, "parent": 13}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 6, "parent": 15}, {"command": 10, "file": 6, "line": 21, "parent": 16}, {"file": 5, "parent": 17}, {"command": 1, "file": 5, "line": 41, "parent": 18}, {"file": 4, "parent": 19}, {"command": 9, "file": 4, "line": 25, "parent": 20}, {"command": 11, "file": 0, "line": 179, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 14, "fragment": "-Wall"}, {"backtrace": 14, "fragment": "-Wextra"}, {"fragment": "-std=gnu99"}], "defines": [{"backtrace": 10, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 21, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}, {"define": "rslidar_msg__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 22, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 22, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py"}, {"backtrace": 22, "path": "/usr/include/python3.10"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "C", "languageStandard": {"backtraces": [5], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 10, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 12, "id": "rslidar_msg__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 12, "id": "rslidar_msg__py::@c80b5731effdf274c015"}], "id": "rslidar_msg__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 8, "fragment": "librslidar_msg__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "C"}, "name": "rslidar_msg__rosidl_generator_py", "nameOnDisk": "librs<PERSON>ar_msg__rosidl_generator_py.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py/rslidar_msg/msg/_rslidar_packet_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}