[0.318s] Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.434s] running egg_info
[0.434s] creating ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info
[0.435s] writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.435s] writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.435s] writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.435s] writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
[0.435s] writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
[0.435s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.436s] reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.436s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.436s] running build
[0.436s] running build_py
[0.436s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.436s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib
[0.436s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.436s] copying ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.437s] copying ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.437s] running install
[0.437s] running install_lib
[0.437s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.437s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.437s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.437s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/__init__.py to __init__.cpython-310.pyc
[0.437s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mavros_bridge_node.py to mavros_bridge_node.cpython-310.pyc
[0.438s] running install_data
[0.438s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index
[0.438s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index
[0.438s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages
[0.438s] copying resource/ardupilot_mavros_bridge -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages
[0.438s] copying package.xml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge
[0.438s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.439s] copying launch/mavros_bridge.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.439s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.439s] copying config/mavros_apm.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.439s] running install_egg_info
[0.439s] Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info
[0.440s] running install_scripts
[0.451s] Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.451s] writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'
[0.467s] Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
