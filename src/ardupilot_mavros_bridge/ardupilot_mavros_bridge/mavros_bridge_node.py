#!/usr/bin/env python3
"""
ArduPilot MAVROS Bridge Node
将ArduPilot飞控的RC输出通道映射到差速小车的cmd_vel话题
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy

from geometry_msgs.msg import Twist
from mavros_msgs.msg import RCOut
from std_msgs.msg import Header

import math


class MavrosBridgeNode(Node):
    """MAVROS到差速小车的桥接节点"""
    
    def __init__(self):
        super().__init__('mavros_bridge_node')
        
        # 声明参数
        self.declare_parameters(
            namespace='',
            parameters=[
                # RC通道参数
                ('throttle_channel', 1),  # 油门通道 (通常是通道1)
                ('steering_channel', 3),  # 转向通道 (通常是通道3)
                
                # PWM范围参数
                ('pwm_min', 1000),        # PWM最小值
                ('pwm_max', 2000),        # PWM最大值
                ('pwm_neutral', 1500),    # PWM中性值
                ('pwm_deadzone', 50),     # PWM死区
                
                # 速度映射参数
                ('max_linear_speed', 2.0),   # 最大线速度 (m/s)
                ('max_angular_speed', 2.0),  # 最大角速度 (rad/s)
                
                # 话题名称
                ('rc_out_topic', '/mavros/rc/out'),
                ('cmd_vel_topic', '/cmd_vel'),
                
                # 发布频率
                ('publish_rate', 20.0),  # Hz
                
                # 安全参数
                ('timeout_duration', 1.0),  # 超时时间(秒)
                ('enable_safety_check', True),
            ]
        )
        
        # 获取参数
        self.throttle_channel = self.get_parameter('throttle_channel').value - 1  # 转换为0索引
        self.steering_channel = self.get_parameter('steering_channel').value - 1  # 转换为0索引
        
        self.pwm_min = self.get_parameter('pwm_min').value
        self.pwm_max = self.get_parameter('pwm_max').value
        self.pwm_neutral = self.get_parameter('pwm_neutral').value
        self.pwm_deadzone = self.get_parameter('pwm_deadzone').value
        
        self.max_linear_speed = self.get_parameter('max_linear_speed').value
        self.max_angular_speed = self.get_parameter('max_angular_speed').value
        
        self.rc_out_topic = self.get_parameter('rc_out_topic').value
        self.cmd_vel_topic = self.get_parameter('cmd_vel_topic').value
        
        self.publish_rate = self.get_parameter('publish_rate').value
        self.timeout_duration = self.get_parameter('timeout_duration').value
        self.enable_safety_check = self.get_parameter('enable_safety_check').value
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # 创建订阅者和发布者
        self.rc_out_subscriber = self.create_subscription(
            RCOut,
            self.rc_out_topic,
            self.rc_out_callback,
            qos_profile
        )
        
        self.cmd_vel_publisher = self.create_publisher(
            Twist,
            self.cmd_vel_topic,
            10
        )
        
        # 创建定时器用于安全检查
        if self.enable_safety_check:
            self.safety_timer = self.create_timer(
                1.0 / self.publish_rate,
                self.safety_check_callback
            )
        
        # 状态变量
        self.last_rc_time = self.get_clock().now()
        self.last_twist = Twist()
        self.rc_data_received = False
        
        self.get_logger().info(f'MAVROS Bridge Node启动')
        self.get_logger().info(f'监听RC输出话题: {self.rc_out_topic}')
        self.get_logger().info(f'发布cmd_vel话题: {self.cmd_vel_topic}')
        self.get_logger().info(f'油门通道: {self.throttle_channel + 1}, 转向通道: {self.steering_channel + 1}')
        
    def rc_out_callback(self, msg: RCOut):
        """处理RC输出消息"""
        try:
            # 检查通道数量
            if len(msg.channels) <= max(self.throttle_channel, self.steering_channel):
                self.get_logger().warn(
                    f'RC通道数量不足: {len(msg.channels)}, '
                    f'需要至少 {max(self.throttle_channel, self.steering_channel) + 1} 个通道'
                )
                return
            
            # 获取PWM值
            throttle_pwm = msg.channels[self.throttle_channel]
            steering_pwm = msg.channels[self.steering_channel]
            
            # 转换为速度命令
            linear_vel, angular_vel = self.pwm_to_velocity(throttle_pwm, steering_pwm)
            
            # 创建Twist消息
            twist_msg = Twist()
            twist_msg.linear.x = linear_vel
            twist_msg.angular.z = angular_vel
            
            # 发布消息
            self.cmd_vel_publisher.publish(twist_msg)
            
            # 更新状态
            self.last_rc_time = self.get_clock().now()
            self.last_twist = twist_msg
            self.rc_data_received = True
            
            # 调试信息
            if self.get_logger().get_effective_level() <= 10:  # DEBUG level
                self.get_logger().debug(
                    f'PWM - 油门: {throttle_pwm}, 转向: {steering_pwm} | '
                    f'速度 - 线性: {linear_vel:.3f}, 角度: {angular_vel:.3f}'
                )
                
        except Exception as e:
            self.get_logger().error(f'处理RC输出时出错: {str(e)}')
    
    def pwm_to_velocity(self, throttle_pwm: int, steering_pwm: int) -> tuple:
        """将PWM值转换为线速度和角速度"""
        
        # 限制PWM值范围
        throttle_pwm = max(self.pwm_min, min(self.pwm_max, throttle_pwm))
        steering_pwm = max(self.pwm_min, min(self.pwm_max, steering_pwm))
        
        # 计算归一化值 (-1.0 到 1.0)
        throttle_normalized = self.normalize_pwm(throttle_pwm)
        steering_normalized = self.normalize_pwm(steering_pwm)
        
        # 应用死区
        throttle_normalized = self.apply_deadzone(throttle_normalized)
        steering_normalized = self.apply_deadzone(steering_normalized)
        
        # 转换为速度
        linear_vel = throttle_normalized * self.max_linear_speed
        angular_vel = -steering_normalized * self.max_angular_speed  # 负号用于正确的转向方向
        
        return linear_vel, angular_vel
    
    def normalize_pwm(self, pwm_value: int) -> float:
        """将PWM值归一化到-1.0到1.0范围"""
        if pwm_value < self.pwm_neutral:
            # 负值范围
            return (pwm_value - self.pwm_neutral) / (self.pwm_neutral - self.pwm_min)
        else:
            # 正值范围
            return (pwm_value - self.pwm_neutral) / (self.pwm_max - self.pwm_neutral)
    
    def apply_deadzone(self, normalized_value: float) -> float:
        """应用死区"""
        deadzone_normalized = self.pwm_deadzone / (self.pwm_max - self.pwm_min) * 2.0
        
        if abs(normalized_value) < deadzone_normalized:
            return 0.0
        elif normalized_value > 0:
            return (normalized_value - deadzone_normalized) / (1.0 - deadzone_normalized)
        else:
            return (normalized_value + deadzone_normalized) / (1.0 - deadzone_normalized)
    
    def safety_check_callback(self):
        """安全检查回调函数"""
        current_time = self.get_clock().now()
        time_diff = (current_time - self.last_rc_time).nanoseconds / 1e9
        
        if self.rc_data_received and time_diff > self.timeout_duration:
            # 超时，发送停止命令
            stop_twist = Twist()
            self.cmd_vel_publisher.publish(stop_twist)
            
            self.get_logger().warn(
                f'RC数据超时 ({time_diff:.2f}s)，发送停止命令'
            )
            self.rc_data_received = False


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        node = MavrosBridgeNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'节点运行出错: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
