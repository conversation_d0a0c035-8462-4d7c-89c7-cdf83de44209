#!/usr/bin/env python3
"""
ArduPilot MAVROS Bridge Launch文件
启动MAVROS和桥接节点，实现ArduPilot到差速小车的控制
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成launch描述"""
    
    # 获取包路径
    pkg_share = FindPackageShare('ardupilot_mavros_bridge')
    mavros_share = FindPackageShare('mavros')
    
    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',  # 串口连接飞控
        description='FCU连接URL - 串口'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',  # UDP广播给GCS
        description='地面站连接URL - UDP广播'
    )
    
    declare_target_system_id = DeclareLaunchArgument(
        'target_system_id',
        default_value='1',
        description='目标系统ID'
    )
    
    declare_target_component_id = DeclareLaunchArgument(
        'target_component_id',
        default_value='1', 
        description='目标组件ID'
    )
    
    declare_config_file = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([pkg_share, 'config', 'mavros_apm.yaml']),
        description='MAVROS配置文件路径'
    )
    
    declare_log_level = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='日志级别 (debug, info, warn, error)'
    )
    
    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='节点命名空间'
    )
    
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )
    
    # Bridge节点参数
    declare_throttle_channel = DeclareLaunchArgument(
        'throttle_channel',
        default_value='1',
        description='油门通道号 (1-based)'
    )
    
    declare_steering_channel = DeclareLaunchArgument(
        'steering_channel', 
        default_value='3',
        description='转向通道号 (1-based)'
    )
    
    declare_max_linear_speed = DeclareLaunchArgument(
        'max_linear_speed',
        default_value='2.0',
        description='最大线速度 (m/s)'
    )
    
    declare_max_angular_speed = DeclareLaunchArgument(
        'max_angular_speed',
        default_value='2.0', 
        description='最大角速度 (rad/s)'
    )
    
    declare_cmd_vel_topic = DeclareLaunchArgument(
        'cmd_vel_topic',
        default_value='/cmd_vel',
        description='cmd_vel话题名称'
    )
    
    declare_enable_safety = DeclareLaunchArgument(
        'enable_safety',
        default_value='true',
        description='是否启用安全检查'
    )
    
    declare_start_mavros = DeclareLaunchArgument(
        'start_mavros',
        default_value='true',
        description='是否启动MAVROS节点'
    )
    
    # MAVROS节点
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',
        namespace=LaunchConfiguration('namespace'),
        output='screen',
        parameters=[
            LaunchConfiguration('config_file'),
            {
                'fcu_url': LaunchConfiguration('fcu_url'),
                'gcs_url': LaunchConfiguration('gcs_url'), 
                'target_system_id': LaunchConfiguration('target_system_id'),
                'target_component_id': LaunchConfiguration('target_component_id'),
                'use_sim_time': LaunchConfiguration('use_sim_time'),
            }
        ],
        condition=IfCondition(LaunchConfiguration('start_mavros')),
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    # MAVROS Bridge节点
    bridge_node = Node(
        package='ardupilot_mavros_bridge',
        executable='mavros_bridge_node',
        name='mavros_bridge',
        namespace=LaunchConfiguration('namespace'),
        output='screen',
        parameters=[{
            'throttle_channel': LaunchConfiguration('throttle_channel'),
            'steering_channel': LaunchConfiguration('steering_channel'),
            'max_linear_speed': LaunchConfiguration('max_linear_speed'),
            'max_angular_speed': LaunchConfiguration('max_angular_speed'),
            'cmd_vel_topic': LaunchConfiguration('cmd_vel_topic'),
            'enable_safety_check': LaunchConfiguration('enable_safety'),
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'rc_out_topic': '/mavros/rc/out',
        }],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加launch参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_target_system_id)
    ld.add_action(declare_target_component_id)
    ld.add_action(declare_config_file)
    ld.add_action(declare_log_level)
    ld.add_action(declare_namespace)
    ld.add_action(declare_use_sim_time)
    ld.add_action(declare_throttle_channel)
    ld.add_action(declare_steering_channel)
    ld.add_action(declare_max_linear_speed)
    ld.add_action(declare_max_angular_speed)
    ld.add_action(declare_cmd_vel_topic)
    ld.add_action(declare_enable_safety)
    ld.add_action(declare_start_mavros)
    
    # 添加节点
    ld.add_action(mavros_node)
    ld.add_action(bridge_node)
    
    return ld
