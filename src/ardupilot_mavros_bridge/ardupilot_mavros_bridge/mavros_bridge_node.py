#!/usr/bin/env python3
"""
ArduPilot MAVROS Bridge Node
将ArduPilot飞控的RC输出通道映射到差速小车的cmd_vel话题
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy

from geometry_msgs.msg import Twist
from mavros_msgs.msg import RCOut
from std_msgs.msg import Header

import math


class MavrosBridgeNode(Node):
    """MAVROS到差速小车的桥接节点"""
    
    def __init__(self):
        super().__init__('mavros_bridge_node')
        
        # 声明参数
        self.declare_parameters(
            namespace='',
            parameters=[
                # 差速驱动电机通道参数
                ('left_motor_channel', 1),   # 左电机通道 (通常是通道1)
                ('right_motor_channel', 3),  # 右电机通道 (通常是通道3)

                # PWM范围参数
                ('pwm_min', 1000),        # PWM最小值
                ('pwm_max', 2000),        # PWM最大值
                ('pwm_neutral', 1500),    # PWM中性值
                ('pwm_deadzone', 50),     # PWM死区

                # 车辆物理参数
                ('wheelbase', 0.4),          # 轮距 (米)
                ('wheel_radius', 0.1),       # 车轮半径 (米)
                ('max_motor_speed', 2.0),    # 最大电机速度 (m/s)

                # 速度限制参数
                ('max_linear_speed', 2.0),   # 最大线速度 (m/s)
                ('max_angular_speed', 2.0),  # 最大角速度 (rad/s)
                
                # 话题名称
                ('rc_out_topic', '/mavros/rc/out'),
                ('cmd_vel_topic', '/cmd_vel'),
                
                # 发布频率
                ('publish_rate', 20.0),  # Hz
                
                # 安全参数
                ('timeout_duration', 1.0),  # 超时时间(秒)
                ('enable_safety_check', True),
            ]
        )
        
        # 获取参数
        self.left_motor_channel = self.get_parameter('left_motor_channel').value - 1   # 转换为0索引
        self.right_motor_channel = self.get_parameter('right_motor_channel').value - 1 # 转换为0索引

        self.pwm_min = self.get_parameter('pwm_min').value
        self.pwm_max = self.get_parameter('pwm_max').value
        self.pwm_neutral = self.get_parameter('pwm_neutral').value
        self.pwm_deadzone = self.get_parameter('pwm_deadzone').value

        # 车辆物理参数
        self.wheelbase = self.get_parameter('wheelbase').value
        self.wheel_radius = self.get_parameter('wheel_radius').value
        self.max_motor_speed = self.get_parameter('max_motor_speed').value

        self.max_linear_speed = self.get_parameter('max_linear_speed').value
        self.max_angular_speed = self.get_parameter('max_angular_speed').value
        
        self.rc_out_topic = self.get_parameter('rc_out_topic').value
        self.cmd_vel_topic = self.get_parameter('cmd_vel_topic').value
        
        self.publish_rate = self.get_parameter('publish_rate').value
        self.timeout_duration = self.get_parameter('timeout_duration').value
        self.enable_safety_check = self.get_parameter('enable_safety_check').value
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # 创建订阅者和发布者
        self.rc_out_subscriber = self.create_subscription(
            RCOut,
            self.rc_out_topic,
            self.rc_out_callback,
            qos_profile
        )
        
        self.cmd_vel_publisher = self.create_publisher(
            Twist,
            self.cmd_vel_topic,
            10
        )
        
        # 创建定时器用于安全检查
        if self.enable_safety_check:
            self.safety_timer = self.create_timer(
                1.0 / self.publish_rate,
                self.safety_check_callback
            )
        
        # 状态变量
        self.last_rc_time = self.get_clock().now()
        self.last_twist = Twist()
        self.rc_data_received = False
        
        self.get_logger().info(f'差速驱动MAVROS Bridge Node启动')
        self.get_logger().info(f'监听RC输出话题: {self.rc_out_topic}')
        self.get_logger().info(f'发布cmd_vel话题: {self.cmd_vel_topic}')
        self.get_logger().info(f'左电机通道: {self.left_motor_channel + 1}, 右电机通道: {self.right_motor_channel + 1}')
        self.get_logger().info(f'车辆参数 - 轮距: {self.wheelbase}m, 车轮半径: {self.wheel_radius}m')
        
    def rc_out_callback(self, msg: RCOut):
        """处理RC输出消息 - 差速驱动版本"""
        try:
            # 检查通道数量
            if len(msg.channels) <= max(self.left_motor_channel, self.right_motor_channel):
                self.get_logger().warn(
                    f'RC通道数量不足: {len(msg.channels)}, '
                    f'需要至少 {max(self.left_motor_channel, self.right_motor_channel) + 1} 个通道'
                )
                return

            # 获取左右电机PWM值
            left_motor_pwm = msg.channels[self.left_motor_channel]
            right_motor_pwm = msg.channels[self.right_motor_channel]

            # 转换为差速驱动速度命令
            linear_vel, angular_vel = self.differential_drive_kinematics(left_motor_pwm, right_motor_pwm)
            
            # 创建Twist消息
            twist_msg = Twist()
            twist_msg.linear.x = linear_vel
            twist_msg.angular.z = angular_vel
            
            # 发布消息
            self.cmd_vel_publisher.publish(twist_msg)
            
            # 更新状态
            self.last_rc_time = self.get_clock().now()
            self.last_twist = twist_msg
            self.rc_data_received = True
            
            # 调试信息
            if self.get_logger().get_effective_level() <= 10:  # DEBUG level
                self.get_logger().debug(
                    f'PWM - 左电机: {left_motor_pwm}, 右电机: {right_motor_pwm} | '
                    f'速度 - 线性: {linear_vel:.3f}, 角度: {angular_vel:.3f}'
                )
                
        except Exception as e:
            self.get_logger().error(f'处理RC输出时出错: {str(e)}')
    
    def differential_drive_kinematics(self, left_motor_pwm: int, right_motor_pwm: int) -> tuple:
        """差速驱动运动学：将左右电机PWM转换为线速度和角速度"""

        # 限制PWM值范围
        left_motor_pwm = max(self.pwm_min, min(self.pwm_max, left_motor_pwm))
        right_motor_pwm = max(self.pwm_min, min(self.pwm_max, right_motor_pwm))

        # 将PWM转换为电机速度 (-max_motor_speed 到 +max_motor_speed)
        left_speed = self.pwm_to_motor_speed(left_motor_pwm)
        right_speed = self.pwm_to_motor_speed(right_motor_pwm)

        # 差速驱动运动学公式
        # 线速度 = (左轮速度 + 右轮速度) / 2
        linear_vel = (left_speed + right_speed) / 2.0

        # 角速度 = (右轮速度 - 左轮速度) / 轮距
        angular_vel = (right_speed - left_speed) / self.wheelbase

        # 应用速度限制
        linear_vel = max(-self.max_linear_speed, min(self.max_linear_speed, linear_vel))
        angular_vel = max(-self.max_angular_speed, min(self.max_angular_speed, angular_vel))

        return linear_vel, angular_vel

    def pwm_to_motor_speed(self, pwm_value: int) -> float:
        """将PWM值转换为电机速度 (m/s)"""

        # 归一化PWM值到 -1.0 到 1.0
        normalized = self.normalize_pwm(pwm_value)

        # 应用死区
        normalized = self.apply_deadzone(normalized)

        # 转换为电机速度
        motor_speed = normalized * self.max_motor_speed

        return motor_speed
    
    def normalize_pwm(self, pwm_value: int) -> float:
        """将PWM值归一化到-1.0到1.0范围"""
        if pwm_value < self.pwm_neutral:
            # 负值范围
            return (pwm_value - self.pwm_neutral) / (self.pwm_neutral - self.pwm_min)
        else:
            # 正值范围
            return (pwm_value - self.pwm_neutral) / (self.pwm_max - self.pwm_neutral)
    
    def apply_deadzone(self, normalized_value: float) -> float:
        """应用死区"""
        deadzone_normalized = self.pwm_deadzone / (self.pwm_max - self.pwm_min) * 2.0
        
        if abs(normalized_value) < deadzone_normalized:
            return 0.0
        elif normalized_value > 0:
            return (normalized_value - deadzone_normalized) / (1.0 - deadzone_normalized)
        else:
            return (normalized_value + deadzone_normalized) / (1.0 - deadzone_normalized)
    
    def safety_check_callback(self):
        """安全检查回调函数"""
        current_time = self.get_clock().now()
        time_diff = (current_time - self.last_rc_time).nanoseconds / 1e9
        
        if self.rc_data_received and time_diff > self.timeout_duration:
            # 超时，发送停止命令
            stop_twist = Twist()
            self.cmd_vel_publisher.publish(stop_twist)
            
            self.get_logger().warn(
                f'RC数据超时 ({time_diff:.2f}s)，发送停止命令'
            )
            self.rc_data_received = False


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        node = MavrosBridgeNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'节点运行出错: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
