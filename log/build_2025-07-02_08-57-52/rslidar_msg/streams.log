[0.006s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.049s] -- The C compiler identification is GNU 11.4.0
[0.085s] -- The CXX compiler identification is GNU 11.4.0
[0.089s] -- Detecting C compiler ABI info
[0.136s] -- Detecting C compiler ABI info - done
[0.140s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.141s] -- Detecting C compile features
[0.141s] -- Detecting C compile features - done
[0.142s] -- Detecting CXX compiler ABI info
[0.193s] -- Detecting CXX compiler ABI info - done
[0.197s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.197s] -- Detecting CXX compile features
[0.198s] -- Detecting CXX compile features - done
[0.202s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.298s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.354s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.376s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.379s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.384s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.392s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.402s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.419s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.443s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.445s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.505s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.523s] -- Found FastRTPS: /opt/ros/humble/include  
[0.553s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.560s] -- Looking for pthread.h
[0.607s] -- Looking for pthread.h - found
[0.607s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.655s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.656s] -- Found Threads: TRUE  
[0.687s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.843s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.064s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.149s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.164s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.166s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.181s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.181s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.181s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.182s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.194s] -- Found PythonExtra: .so  
[1.221s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.234s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.237s] -- Added test 'lint_cmake' to check CMake code style
[1.239s] -- Added test 'xmllint' to check XML markup files
[1.240s] -- Configuring done
[1.254s] -- Generating done
[1.260s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg
[1.266s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[1.268s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.298s] [  3%] [34m[1mGenerating C code for ROS interfaces[0m
[1.306s] [  6%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.311s] [  6%] Built target ament_cmake_python_copy_rslidar_msg
[1.435s] running egg_info
[1.436s] creating rslidar_msg.egg-info
[1.436s] writing rslidar_msg.egg-info/PKG-INFO
[1.436s] writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
[1.436s] writing top-level names to rslidar_msg.egg-info/top_level.txt
[1.436s] writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[1.439s] reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[1.439s] writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[1.450s] [  6%] Built target rslidar_msg__cpp
[1.455s] [  9%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_c.dir/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.c.o[0m
[1.457s] [ 12%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[1.457s] [ 19%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[1.457s] [ 19%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[1.465s] [ 19%] Built target ament_cmake_python_build_rslidar_msg_egg
[1.499s] [ 22%] [32m[1mLinking C shared library librslidar_msg__rosidl_generator_c.so[0m
[1.528s] [ 22%] Built target rslidar_msg__rosidl_generator_c
[1.537s] [ 32%] [34m[1mGenerating C introspection for ROS interfaces[0m
[1.537s] [ 32%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[1.537s] [ 32%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[1.594s] [ 35%] [32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/rslidar_msg/msg/rslidar_packet__type_support.cpp.o[0m
[1.600s] [ 38%] [32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/rslidar_msg/msg/detail/dds_fastrtps/rslidar_packet__type_support.cpp.o[0m
[1.603s] [ 41%] [32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp.o[0m
[1.689s] [ 45%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/rslidar_msg/msg/detail/rslidar_packet__type_support.c.o[0m
[1.712s] [ 48%] [32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_c.dir/rosidl_typesupport_c/rslidar_msg/msg/rslidar_packet__type_support.cpp.o[0m
[1.718s] [ 51%] [32m[1mLinking C shared library librslidar_msg__rosidl_typesupport_introspection_c.so[0m
[1.740s] [ 54%] [32m[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_c.so[0m
[1.747s] [ 54%] Built target rslidar_msg__rosidl_typesupport_introspection_c
[1.749s] [ 58%] [32m[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_cpp.so[0m
[1.776s] [ 58%] Built target rslidar_msg__rosidl_typesupport_c
[1.788s] [ 61%] [32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/rslidar_msg/msg/detail/rslidar_packet__type_support_c.cpp.o[0m
[1.788s] [ 61%] Built target rslidar_msg__rosidl_typesupport_cpp
[1.841s] [ 64%] [32m[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_introspection_cpp.so[0m
[1.865s] [ 67%] [32m[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_fastrtps_cpp.so[0m
[1.892s] [ 67%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp
[1.919s] [ 67%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp
[1.962s] [ 70%] [32m[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_fastrtps_c.so[0m
[2.007s] [ 70%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c
[2.021s] [ 70%] Built target rslidar_msg
[2.036s] [ 74%] [34m[1mGenerating Python code for ROS interfaces[0m
[2.195s] [ 74%] Built target rslidar_msg__py
[2.208s] [ 77%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_py.dir/rosidl_generator_py/rslidar_msg/msg/_rslidar_packet_s.c.o[0m
[2.278s] [ 80%] [32m[1mLinking C shared library rosidl_generator_py/rslidar_msg/librslidar_msg__rosidl_generator_py.so[0m
[2.304s] [ 80%] Built target rslidar_msg__rosidl_generator_py
[2.320s] [ 83%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c.o[0m
[2.320s] [ 87%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[2.320s] [ 90%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[2.379s] [100%] [32m[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[2.379s] [100%] [32m[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[2.379s] [100%] [32m[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[2.411s] [100%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext
[2.412s] [100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext
[2.412s] [100%] Built target rslidar_msg__rosidl_typesupport_c__pyext
[2.420s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[2.426s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[2.433s] -- Install configuration: ""
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.c
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h
[2.433s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh
[2.434s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv
[2.434s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so
[2.434s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so" to ""
[2.434s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.434s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.434s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.434s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h
[2.434s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[2.434s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so
[2.434s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so" to ""
[2.434s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.435s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp
[2.435s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp
[2.435s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.435s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.435s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[2.435s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/dds_fastrtps
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so
[2.436s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so" to ""
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.c
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so
[2.436s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so" to ""
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so
[2.436s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so" to ""
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg
[2.436s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so
[2.436s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so" to ""
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so
[2.436s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so" to ""
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[2.436s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[2.459s] Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...
[2.459s] Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...
[2.459s] Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...
[2.459s] Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py'...
[2.459s] Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py'...
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[2.462s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[2.462s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[2.462s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so
[2.462s] -- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so" to ""
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh
[2.462s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake
[2.463s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake
[2.464s] -- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml
[2.465s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
