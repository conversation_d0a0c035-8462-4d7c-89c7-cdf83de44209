{"artifacts": [{"path": "rslidar_sdk_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "add_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 167, "parent": 0}, {"command": 1, "file": 0, "line": 197, "parent": 0}, {"command": 2, "file": 0, "line": 171, "parent": 0}, {"command": 3, "file": 0, "line": 191, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 4}, {"command": 4, "file": 0, "line": 72, "parent": 0}, {"command": 5, "file": 0, "line": 34, "parent": 0}, {"command": 5, "file": 0, "line": 24, "parent": 0}, {"command": 5, "file": 0, "line": 78, "parent": 0}, {"command": 5, "file": 0, "line": 64, "parent": 0}, {"command": 5, "file": 0, "line": 137, "parent": 0}, {"command": 6, "file": 0, "line": 156, "parent": 0}, {"command": 6, "file": 0, "line": 161, "parent": 0}, {"command": 6, "file": 0, "line": 138, "parent": 0}, {"command": 7, "file": 1, "line": 141, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG   -O3 -std=c++17"}, {"backtrace": 6, "fragment": "-Wall"}], "defines": [{"backtrace": 5, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 7, "define": "ENABLE_DIFOP_PARSE"}, {"backtrace": 8, "define": "ENABLE_MODIFY_RECVBUF"}, {"backtrace": 9, "define": "POINT_TYPE_XYZI"}, {"backtrace": 10, "define": "PROJECT_PATH=\"/home/<USER>/ros_ws/src/rslidar_sdk\""}, {"backtrace": 5, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 11, "define": "ROS2_FOUND"}], "includes": [{"backtrace": 12, "path": "/home/<USER>/ros_ws/src/rslidar_sdk/src"}, {"backtrace": 13, "path": "/home/<USER>/ros_ws/src/rslidar_sdk/src/rs_driver/src"}, {"backtrace": 13, "path": "/usr/local/rslidar_sdk/include"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 15, "isSystem": true, "path": "/home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg"}, {"backtrace": 15, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 15, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [5], "standard": "17"}, "sourceIndexes": [0, 1]}], "id": "rslidar_sdk_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/rslidar_sdk"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_sdk"}}, "link": {"commandFragments": [{"fragment": "-O3 -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:/home/<USER>/ros_ws/install/rslidar_msg/lib:", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libyaml-cpp.so.0.7.0", "role": "libraries"}, {"backtrace": 3, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 3, "fragment": "-lpcap", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "rslidar_sdk_node", "nameOnDisk": "rslidar_sdk_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "node/rslidar_sdk_node.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/manager/node_manager.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}