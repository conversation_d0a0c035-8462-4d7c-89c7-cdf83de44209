/**:
  ros__parameters:
    plugin_denylist:
      # 禁用不需要的插件 - 针对地面/水面差速车辆优化
      # common plugins
      - actuator_control      # 不需要直接控制执行器
      - ftp                   # 文件传输不需要
      - hil                   # 硬件在环仿真不需要
      # extras plugins
      - altitude              # 高度传感器不需要（地面/水面车辆）
      - debug_value           # 调试值不需要
      - image_pub             # 图像发布不需要
      - px4flow               # PX4光流传感器不需要
      - vibration             # 振动传感器不需要
      - vision_speed_estimate # 视觉速度估计不需要
      - landing_target        # 着陆目标不需要（地面/水面车辆）
      - mount_control         # 云台控制不需要
      - mocap                 # 动作捕捉不需要
      - fake_gps              # 虚假GPS不需要
      - adsb                  # ADS-B不需要（地面/水面车辆）
      - camera                # 相机控制不需要

    # 启用的关键插件（白名单方式）
    plugin_allowlist:
      # 系统核心插件
      - 'sys_*'               # 系统状态和时间同步
      # 位置和导航
      - global_position       # 全局位置（GPS）
      - local_position        # 本地位置
      - imu                   # IMU数据
      # 控制和设定点
      - setpoint_position     # 位置设定点
      - setpoint_velocity     # 速度设定点
      - setpoint_raw          # 原始设定点
      - guided_target         # 引导目标
      # 任务和航线
      - mission               # 航线任务
      - waypoint              # 航点
      # RC和手动控制
      - rc                    # RC输入输出
      - manual_control        # 手动控制
      # 参数和命令
      - param                 # 参数管理
      - command               # 命令接口
      # 状态和诊断
      - vfr_hud               # VFR HUD数据
      - radio_status          # 无线电状态
      # 车轮里程计（差速车辆专用）
      - wheel_odometry        # 车轮里程计
      # 距离传感器（避障用）
      - distance_sensor       # 距离传感器
