[0.000000] (-) TimerEvent: {}
[0.000437] (rslidar_msg) JobQueued: {'identifier': 'rslidar_msg', 'dependencies': OrderedDict()}
[0.000471] (rslidar_sdk) JobQueued: {'identifier': 'rslidar_sdk', 'dependencies': OrderedDict([('rslidar_msg', '/home/<USER>/ros_ws/install/rslidar_msg')])}
[0.000530] (rslidar_msg) JobStarted: {'identifier': 'rslidar_msg'}
[0.005213] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'cmake'}
[0.006133] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'build'}
[0.006171] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_msg', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '*************** 61837 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '3'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '*************** 61837 *************** 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg')]), 'shell': False}
[0.069227] (rslidar_msg) StdoutLine: {'line': b'[  3%] Built target rslidar_msg__cpp\n'}
[0.073151] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target rslidar_msg__rosidl_generator_c\n'}
[0.074537] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_rslidar_msg\n'}
[0.085054] (rslidar_msg) StdoutLine: {'line': b'[ 22%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp\n'}
[0.087587] (rslidar_msg) StdoutLine: {'line': b'[ 32%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp\n'}
[0.088992] (rslidar_msg) StdoutLine: {'line': b'[ 51%] Built target rslidar_msg__rosidl_typesupport_introspection_c\n'}
[0.089113] (rslidar_msg) StdoutLine: {'line': b'[ 51%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c\n'}
[0.089399] (rslidar_msg) StdoutLine: {'line': b'[ 61%] Built target rslidar_msg__rosidl_typesupport_cpp\n'}
[0.091559] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg__rosidl_typesupport_c\n'}
[0.099896] (-) TimerEvent: {}
[0.104640] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg\n'}
[0.122385] (rslidar_msg) StdoutLine: {'line': b'[ 74%] Built target rslidar_msg__py\n'}
[0.139840] (rslidar_msg) StdoutLine: {'line': b'[ 80%] Built target rslidar_msg__rosidl_generator_py\n'}
[0.157822] (rslidar_msg) StdoutLine: {'line': b'[ 87%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext\n'}
[0.159902] (rslidar_msg) StdoutLine: {'line': b'[ 93%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.160052] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext\n'}
[0.199140] (rslidar_msg) StdoutLine: {'line': b'running egg_info\n'}
[0.199588] (rslidar_msg) StdoutLine: {'line': b'writing rslidar_msg.egg-info/PKG-INFO\n'}
[0.199704] (rslidar_msg) StdoutLine: {'line': b'writing dependency_links to rslidar_msg.egg-info/dependency_links.txt\n'}
[0.199798] (rslidar_msg) StdoutLine: {'line': b'writing top-level names to rslidar_msg.egg-info/top_level.txt\n'}
[0.199946] (-) TimerEvent: {}
[0.200923] (rslidar_msg) StdoutLine: {'line': b"reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[0.201264] (rslidar_msg) StdoutLine: {'line': b"writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[0.218110] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_rslidar_msg_egg\n'}
[0.227599] (rslidar_msg) CommandEnded: {'returncode': 0}
[0.228449] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'install'}
[0.234042] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '*************** 61837 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '3'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '*************** 61837 *************** 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg')]), 'shell': False}
[0.239228] (rslidar_msg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.239488] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg\n'}
[0.239564] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.239614] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.239676] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h\n'}
[0.239714] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.239758] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h\n'}
[0.239790] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h\n'}
[0.239815] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h\n'}
[0.239840] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.c\n'}
[0.239865] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h\n'}
[0.239889] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh\n'}
[0.239923] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv\n'}
[0.240024] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so\n'}
[0.240098] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.240132] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.240169] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.240207] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h\n'}
[0.240234] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.240370] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so\n'}
[0.240442] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.240467] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.240493] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.240516] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp\n'}
[0.240541] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.240589] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp\n'}
[0.240616] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp\n'}
[0.240639] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp\n'}
[0.240661] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp\n'}
[0.240683] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.240706] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.240727] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.240748] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.240769] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/dds_fastrtps\n'}
[0.240790] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.240854] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.240887] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.240937] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.240962] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.240986] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.241013] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h\n'}
[0.241049] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.c\n'}
[0.241107] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so\n'}
[0.241273] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so\n'}
[0.241324] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[0.241358] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[0.241398] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[0.241423] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.241445] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp\n'}
[0.241654] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so\n'}
[0.241842] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so\n'}
[0.241892] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh\n'}
[0.241930] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv\n'}
[0.241962] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info\n'}
[0.241986] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.242023] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.242061] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.242097] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.242156] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg\n'}
[0.242229] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py\n'}
[0.242270] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.242296] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.242321] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c\n'}
[0.242355] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.242400] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so\n'}
[0.242427] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg\n'}
[0.242452] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py\n'}
[0.242477] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py\n'}
[0.242501] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c\n'}
[0.242525] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.242561] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.261400] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...\n"}
[0.261470] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...\n"}
[0.264126] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.264212] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.264252] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.264436] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so\n'}
[0.264511] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl\n'}
[0.264545] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg\n'}
[0.264594] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg\n'}
[0.264633] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg\n'}
[0.264660] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh\n'}
[0.264712] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv\n'}
[0.264760] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh\n'}
[0.264786] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv\n'}
[0.264809] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash\n'}
[0.264832] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh\n'}
[0.264853] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh\n'}
[0.264876] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv\n'}
[0.264899] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv\n'}
[0.264934] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg\n'}
[0.265292] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake\n'}
[0.265331] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake\n'}
[0.265469] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.265508] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.265628] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake\n'}
[0.265777] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.265813] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.265977] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.266016] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.266099] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake\n'}
[0.266134] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.266235] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.266266] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.266355] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake\n'}
[0.266389] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.266555] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake\n'}
[0.266592] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.266628] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake\n'}
[0.266654] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.266679] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.266713] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.266762] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.266787] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.266810] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.266833] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake\n'}
[0.266855] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake\n'}
[0.266877] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml\n'}
[0.268406] (rslidar_msg) CommandEnded: {'returncode': 0}
[0.279730] (rslidar_msg) JobEnded: {'identifier': 'rslidar_msg', 'rc': 0}
[0.279950] (rslidar_sdk) JobStarted: {'identifier': 'rslidar_sdk'}
[0.284858] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'cmake'}
[0.285577] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'build'}
[0.285752] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_sdk', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '*************** 61837 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '3'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '*************** 61837 *************** 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk')]), 'shell': False}
[0.300088] (-) TimerEvent: {}
[0.336893] (rslidar_sdk) StdoutLine: {'line': b'[100%] Built target rslidar_sdk_node\n'}
[0.343731] (rslidar_sdk) CommandEnded: {'returncode': 0}
[0.344277] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'install'}
[0.344386] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '*************** 61837 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '3'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '*************** 61837 *************** 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble:/home/<USER>/ros_ws/install/rslidar_sdk')]), 'shell': False}
[0.349606] (rslidar_sdk) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.349978] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node\n'}
[0.350066] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch\n'}
[0.350104] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py\n'}
[0.350145] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch\n'}
[0.350172] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py\n'}
[0.350215] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz\n'}
[0.350242] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz\n'}
[0.350266] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz\n'}
[0.350303] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk\n'}
[0.350329] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk\n'}
[0.350364] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh\n'}
[0.350408] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv\n'}
[0.350434] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh\n'}
[0.350460] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv\n'}
[0.350484] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash\n'}
[0.350509] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh\n'}
[0.350542] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh\n'}
[0.350584] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv\n'}
[0.350608] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv\n'}
[0.350630] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk\n'}
[0.350653] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake\n'}
[0.350675] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake\n'}
[0.350697] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml\n'}
[0.351575] (rslidar_sdk) CommandEnded: {'returncode': 0}
[0.357162] (rslidar_sdk) JobEnded: {'identifier': 'rslidar_sdk', 'rc': 0}
[0.357456] (-) EventReactorShutdown: {}
