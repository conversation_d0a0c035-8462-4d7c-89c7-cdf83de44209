{"artifacts": [{"path": "librslidar_msg__rosidl_typesupport_fastrtps_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "add_dependencies", "add_compile_options", "target_compile_options", "add_definitions", "find_package", "target_include_directories", "set_target_properties"], "files": ["/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 116, "parent": 4}, {"command": 4, "file": 0, "line": 185, "parent": 4}, {"command": 5, "file": 0, "line": 154, "parent": 4}, {"command": 5, "file": 0, "line": 154, "parent": 4}, {"command": 5, "file": 0, "line": 144, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 4, "parent": 10}, {"command": 6, "file": 4, "line": 139, "parent": 11}, {"command": 7, "file": 3, "line": 15, "parent": 0}, {"command": 8, "file": 0, "line": 136, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 7, "parent": 15}, {"command": 10, "file": 7, "line": 21, "parent": 16}, {"file": 6, "parent": 17}, {"command": 1, "file": 6, "line": 41, "parent": 18}, {"file": 5, "parent": 19}, {"command": 9, "file": 5, "line": 25, "parent": 20}, {"command": 11, "file": 0, "line": 138, "parent": 4}, {"command": 5, "file": 0, "line": 159, "parent": 4}, {"command": 12, "file": 0, "line": 125, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 13, "fragment": "-Wall"}, {"backtrace": 13, "fragment": "-Wextra"}, {"backtrace": 13, "fragment": "-Wpedantic"}, {"backtrace": 14, "fragment": "-Wredundant-decls"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 9, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_CPP_BUILDING_DLL_rslidar_msg"}, {"backtrace": 21, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 22, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 23, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [24], "standard": "14"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 12, "id": "rslidar_msg__cpp::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "rslidar_msg__rosidl_typesupport_fastrtps_cpp", "nameOnDisk": "librslidar_msg__rosidl_typesupport_fastrtps_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_cpp/rslidar_msg/msg/detail/dds_fastrtps/rslidar_packet__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_cpp/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_cpp/rslidar_msg/msg/detail/dds_fastrtps/rslidar_packet__type_support.cpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}