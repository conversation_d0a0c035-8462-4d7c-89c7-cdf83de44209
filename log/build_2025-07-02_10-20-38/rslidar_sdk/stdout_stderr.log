[0m=============================================================[0m
[0m-- POINT_TYPE is XYZI[0m
[0m=============================================================[0m
[0m=============================================================[0m
[0m-- ROS Not Found. ROS Support is turned Off.[0m
[0m=============================================================[0m
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0m=============================================================[0m
[0m-- ROS2 Found. ROS2 Support is turned On.[0m
[0m=============================================================[0m
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)
[0m=============================================================[0m
[0m-- CMake run for UNIX GNU Compiler[0m
[0m=============================================================[0m
[0m=============================================================[0m
[0m-- rs_driver Version : v1.5.17[0m
[0m=============================================================[0m
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk
[35m[1mConsolidate compiler generated dependencies of target rslidar_sdk_node[0m
[100%] Built target rslidar_sdk_node
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
