[0.048s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.049s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7bba2e9ee3b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7bba2e977b20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7bba2e977b20>>)
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.104s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.104s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.104s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.112s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.116s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.catkin' and name 'rslidar_sdk'
[0.116s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.116s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.116s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.116s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.116s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.128s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': False, 'test_result_base': None}
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.128s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.128s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': False, 'test_result_base': None}
[0.128s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.129s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.129s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.129s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.130s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.130s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.130s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.131s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'catkin'
[0.131s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[0.131s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.131s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.135s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.137s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.255s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '1': /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.256s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'ros_package_path')
[0.256s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/ros_package_path.ps1'
[0.257s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/ros_package_path.dsv'
[0.257s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/ros_package_path.sh'
[0.258s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'pkg_config_path')
[0.258s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path.ps1'
[0.259s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path.dsv'
[0.259s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path.sh'
[0.261s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'pkg_config_path_multiarch')
[0.261s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path_multiarch.ps1'
[0.262s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path_multiarch.dsv'
[0.262s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/pkg_config_path_multiarch.sh'
[0.263s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.265s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.266s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.266s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.266s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.267s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.267s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.267s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.267s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.267s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.268s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.269s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.269s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.269s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.270s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.270s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.270s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.271s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.313s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.313s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.313s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[0.314s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.318s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.318s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.318s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.324s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.324s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[0.325s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[0.349s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[0.349s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[0.350s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[0.350s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[0.351s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[0.351s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[0.351s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[0.352s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
