#!/usr/bin/env python3
"""
简单测试Launch文件
使用基本的MAVROS配置进行测试
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """生成简单测试launch描述"""
    
    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',
        description='飞控连接URL'
    )
    
    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url', 
        default_value='udp-b://@14550',
        description='地面站连接URL'
    )
    
    # MAVROS节点 - 使用最基本的配置
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',
        output='screen',
        parameters=[{
            'fcu_url': LaunchConfiguration('fcu_url'),
            'gcs_url': LaunchConfiguration('gcs_url'), 
            'target_system_id': 1,
            'target_component_id': 1,
            'fcu_protocol': 'v2.0',
        }],
        arguments=['--ros-args', '--log-level', 'info']
    )
    
    # 桥接节点
    bridge_node = Node(
        package='ardupilot_mavros_bridge',
        executable='mavros_bridge_node',
        name='mavros_bridge',
        output='screen',
        parameters=[{
            'throttle_channel': 1,
            'steering_channel': 3,
            'max_linear_speed': 2.0,
            'max_angular_speed': 2.0,
            'cmd_vel_topic': '/cmd_vel',
            'rc_out_topic': '/mavros/rc/out',
            'enable_safety_check': True,
        }],
        arguments=['--ros-args', '--log-level', 'info']
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加launch参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    
    # 添加节点
    ld.add_action(mavros_node)
    ld.add_action(bridge_node)
    
    return ld
