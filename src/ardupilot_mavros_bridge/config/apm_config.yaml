# 简化的ArduPilot配置文件
# 最小化配置，避免插件冲突
#
/**:
  ros__parameters:
    startup_px4_usb_quirk: false

# 系统状态
/**/sys:
  ros__parameters:
    min_voltage: [10.0]
    disable_diag: false
    heartbeat_rate: 1.0
    conn_timeout: 10.0

# 时间同步
/**/time:
  ros__parameters:
    time_ref_source: "fcu"
    timesync_mode: MAVLINK
    timesync_rate: 10.0
    system_time_rate: 1.0

# --- MAVROS 插件配置 (按字母顺序) ---

# 无线电状态
/**/tdr_radio:
  ros__parameters:
    low_rssi: 40  # RSSI低电平诊断阈值

# 命令接口
/**/cmd:
  ros__parameters:
    use_comp_id_system_control: false   # 兼容老版本飞控

# 全局位置 (GPS)
/**/global_position:
  ros__parameters:
    frame_id: "map"               # 原点坐标系
    child_frame_id: "base_link"   # 车体坐标系
    rot_covariance: 99999.0       # 姿态协方差
    gps_uere: 1.0                 # GPS用户等效距离误差 (m)
    use_relative_alt: true        # 使用相对高度（地面车辆推荐）
    tf.send: true                 # 发送TF变换
    tf.frame_id: "map"            # TF原点坐标系
    tf.global_frame_id: "earth"   # TF地球坐标系
    tf.child_frame_id: "base_link"  # TF车体坐标系

# IMU数据发布
/**/imu:
  ros__parameters:
    frame_id: "base_link"
    # 针对地面车辆调整的IMU参数
    linear_acceleration_stdev: 0.001      # 线性加速度标准差
    angular_velocity_stdev: 0.001745329   # 角速度标准差 (0.1度)
    orientation_stdev: 0.1                # 方向标准差 (适合地面车辆)
    magnetic_stdev: 0.0                   # 磁力计标准差

# 本地位置
/**/local_position:
  ros__parameters:
    frame_id: "map"                       # 本地坐标系
    tf.send: true                         # 发送TF变换
    tf.frame_id: "map"                    # TF坐标系
    tf.child_frame_id: "base_link"        # TF子坐标系
    tf.send_fcu: false                    # 不向飞控发送位置

# 参数管理
# 用于飞控参数读写

# RC输入输出
# RC通道数据处理

# 加速度设定点
/**/setpoint_accel:
  ros__parameters:
    send_force: false               # 不发送力控制

# 姿态设定点 (地面车辆主要用于航向控制)
/**/setpoint_attitude:
  ros__parameters:
    reverse_thrust: true            # 允许反向推力 (差速车辆)
    use_quaternion: false           # 使用欧拉角而非四元数
    tf.listen: false                # 禁用TF监听
    tf.frame_id: "map"
    tf.child_frame_id: "target_attitude"
    tf.rate_limit: 20.0             # 降低频率适合地面车辆

# 原始设定点
/**/setpoint_raw:
  ros__parameters:
    thrust_scaling: 1.0             # 推力缩放 (归一化 0-1)

# 位置设定点 (用于航点导航)
/**/setpoint_position:
  ros__parameters:
    tf.listen: false                # 禁用TF监听，使用话题
    tf.frame_id: "map"
    tf.child_frame_id: "target_position"
    tf.rate_limit: 10.0             # 适合地面车辆的更新频率
    mav_frame: LOCAL_NED            # 本地NED坐标系

# 引导目标 (用于实时目标点控制)
/**/guided_target:
  ros__parameters:
    tf.listen: false                # 禁用TF监听，使用话题
    tf.frame_id: "map"
    tf.child_frame_id: "guided_target"
    tf.rate_limit: 10.0             # 引导目标更新频率

# 速度设定点 (用于速度控制)
/**/setpoint_velocity:
  ros__parameters:
    mav_frame: LOCAL_NED            # 本地NED坐标系

# VFR HUD数据
# 提供速度、高度、航向等飞行数据

# 任务和航线管理
/**/mission:
  ros__parameters:
    pull_after_gcs: true            # GCS更新任务后自动拉取
    use_mission_item_int: true      # 使用MISSION_ITEM_INT消息
    reschedule_pull: true           # 重新调度拉取
    pull_after_gcs_timeout: 30.0    # GCS更新后拉取超时时间

# --- mavros extras plugins (same order) ---

# adsb
# None

# debug_value
# None

# 距离传感器 (用于避障和地形感知)
## 可用方向: http://wiki.ros.org/mavros/Enumerations
/**/distance_sensor:
  ros__parameters:
    config: |
      # 前向距离传感器 (避障用)
      rangefinder_front:
        id: 0
        frame_id: "front_laser"
        orientation: PITCH_0      # 前向
        field_of_view: 0.1        # 视场角 (弧度)
        send_tf: true
        sensor_position: {x: 0.3, y: 0.0, z: 0.1}  # 传感器位置
      # 后向距离传感器
      rangefinder_back:
        id: 1
        frame_id: "back_laser"
        orientation: PITCH_180    # 后向
        field_of_view: 0.1
        send_tf: true
        sensor_position: {x: -0.3, y: 0.0, z: 0.1}
      # 下向距离传感器 (地面检测)
      rangefinder_down:
        subscriber: true
        id: 2
        frame_id: "down_laser"
        orientation: PITCH_270    # 向下 (APM 3.4+支持)
        field_of_view: 0.05
        send_tf: true
        sensor_position: {x: 0.0, y: 0.0, z: -0.1}

# 手动控制 (RC控制)
/**/manual_control:
  ros__parameters:
    # RC通道映射 (ArduPilot Rover标准)
    channel_map:
      roll: 1         # 转向通道 (通常对应左右转向)
      pitch: 2        # 前后通道 (通常对应前进后退)
      throttle: 3     # 油门通道 (通常对应速度)
      yaw: 4          # 偏航通道 (通常对应原地转向)
      mode: 5         # 模式切换通道
      aux1: 6         # 辅助通道1
      aux2: 7         # 辅助通道2
      aux3: 8         # 辅助通道3
    # PWM范围设置
    pwm_min: 1000     # PWM最小值
    pwm_max: 2000     # PWM最大值
    pwm_neutral: 1500 # PWM中性值

# 里程计融合
/**/odometry:
  ros__parameters:
    fcu.odom_parent_id_des: "odom"    # 里程计父坐标系
    fcu.odom_child_id_des: "base_link" # 里程计子坐标系

# 车轮里程计 (差速车辆专用)
/**/wheel_odometry:
  ros__parameters:
    count: 2                          # 车轮数量 (差速驱动)
    use_rpm: false                    # 使用累积距离而非RPM
    # 车轮配置 (根据实际车辆调整)
    wheel0: {x: 0.0, y: -0.2, radius: 0.1}  # 左轮: x偏移, y偏移(m,NED), 半径(m)
    wheel1: {x: 0.0, y:  0.2, radius: 0.1}  # 右轮: x偏移, y偏移(m,NED), 半径(m)
    send_raw: true                    # 发送原始RPM和距离数据
    send_twist: true                  # 发送Twist消息 (适合ROS导航)
    frame_id: "odom"                  # 里程计坐标系
    child_frame_id: "base_link"       # 车体坐标系
    vel_error: 0.05                   # 车轮速度测量误差 1-std (m/s)
    tf.send: true                     # 发送TF变换
    tf.frame_id: "odom"               # TF里程计坐标系
    tf.child_frame_id: "base_link"    # TF车体坐标系

# 配置文件结束
# 针对差速无人车/无人船优化完成
