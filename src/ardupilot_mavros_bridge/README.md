# ArduPilot MAVROS Bridge

这个ROS2功能包实现了ArduPilot飞控与差速小车之间的桥接，将飞控的RC输出通道映射到标准的`cmd_vel`话题。

## 功能特性

- 从MAVROS获取RC输出通道数据（通道1和通道3）
- 将PWM信号转换为差速小车的线速度和角速度
- 发布标准的`geometry_msgs/Twist`消息到`cmd_vel`话题
- 支持安全超时检查
- 可配置的PWM范围和速度映射
- 专门为ArduPilot优化的MAVROS配置

## 系统要求

- ROS2 Humble
- MAVROS (ROS2版本)
- ArduPilot飞控系统
- Python 3.8+

## 安装

1. 确保已安装MAVROS：
```bash
sudo apt install ros-humble-mavros ros-humble-mavros-extras
```

2. 克隆或复制此功能包到您的ROS2工作空间：
```bash
cd ~/ros_ws/src
# 功能包已经在此目录中
```

3. 编译功能包：
```bash
cd ~/ros_ws
colcon build --packages-select ardupilot_mavros_bridge
source install/setup.bash
```

## 配置

### ArduPilot配置

在ArduPilot中，确保以下参数正确设置：

1. **SERVO功能映射**：
   - `SERVO1_FUNCTION = 73` (ThrottleLeft - 左侧电机)
   - `SERVO3_FUNCTION = 74` (ThrottleRight - 右侧电机)

2. **差速驱动模式**：
   - `MOT_PWM_TYPE = 0` (Normal PWM)
   - `FRAME_CLASS = 1` (Rover)
   - `FRAME_TYPE = 1` (Skid Steering)

3. **RC输入配置**：
   - 确保RC接收机正确连接
   - 校准RC通道范围

### MAVROS配置

配置文件位于`config/mavros_apm.yaml`，主要参数：

- `fcu_url`: 飞控连接URL（默认：`udp://:14550@`）
- `target_system_id`: 目标系统ID（默认：1）
- RC通道映射和PWM范围设置

## 使用方法

### 基本启动

启动MAVROS和桥接节点：

```bash
ros2 launch ardupilot_mavros_bridge mavros_bridge.launch.py
```

### 自定义参数启动

```bash
ros2 launch ardupilot_mavros_bridge mavros_bridge.launch.py \
    fcu_url:=udp://:14550@ \
    throttle_channel:=1 \
    steering_channel:=3 \
    max_linear_speed:=2.0 \
    max_angular_speed:=2.0 \
    cmd_vel_topic:=/cmd_vel
```

### 仅启动桥接节点（MAVROS已运行）

```bash
ros2 launch ardupilot_mavros_bridge mavros_bridge.launch.py start_mavros:=false
```

### 单独运行桥接节点

```bash
ros2 run ardupilot_mavros_bridge mavros_bridge_node
```

## 参数说明

### 桥接节点参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `throttle_channel` | int | 1 | 油门通道号（1-based） |
| `steering_channel` | int | 3 | 转向通道号（1-based） |
| `pwm_min` | int | 1000 | PWM最小值 |
| `pwm_max` | int | 2000 | PWM最大值 |
| `pwm_neutral` | int | 1500 | PWM中性值 |
| `pwm_deadzone` | int | 50 | PWM死区 |
| `max_linear_speed` | double | 2.0 | 最大线速度 (m/s) |
| `max_angular_speed` | double | 2.0 | 最大角速度 (rad/s) |
| `cmd_vel_topic` | string | "/cmd_vel" | 输出话题名 |
| `rc_out_topic` | string | "/mavros/rc/out" | RC输出话题名 |
| `timeout_duration` | double | 1.0 | 安全超时时间 (秒) |
| `enable_safety_check` | bool | true | 启用安全检查 |

### Launch参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `fcu_url` | "udp://:14550@" | 飞控连接URL |
| `config_file` | mavros_apm.yaml | MAVROS配置文件 |
| `start_mavros` | true | 是否启动MAVROS |
| `namespace` | "" | 节点命名空间 |
| `log_level` | "info" | 日志级别 |

## 话题接口

### 订阅话题

- `/mavros/rc/out` (`mavros_msgs/RCOut`): RC输出通道数据

### 发布话题

- `/cmd_vel` (`geometry_msgs/Twist`): 差速小车速度命令

## 故障排除

### 常见问题

1. **无法连接到飞控**
   - 检查`fcu_url`参数是否正确
   - 确认飞控已启动并配置了正确的通信端口
   - 检查防火墙设置

2. **没有RC输出数据**
   - 确认MAVROS已成功连接到飞控
   - 检查ArduPilot的SERVO功能配置
   - 验证RC接收机工作正常

3. **速度映射不正确**
   - 调整PWM范围参数（`pwm_min`, `pwm_max`, `pwm_neutral`）
   - 修改最大速度参数
   - 检查死区设置

4. **安全超时触发**
   - 检查RC信号稳定性
   - 调整`timeout_duration`参数
   - 确认飞控正常工作

### 调试命令

查看话题数据：
```bash
# 查看RC输出
ros2 topic echo /mavros/rc/out

# 查看cmd_vel输出
ros2 topic echo /cmd_vel

# 查看MAVROS状态
ros2 topic echo /mavros/state
```

查看节点信息：
```bash
# 查看节点列表
ros2 node list

# 查看节点信息
ros2 node info /mavros_bridge
```

## 开发和贡献

如需修改或扩展功能，请参考源代码中的注释。主要文件：

- `mavros_bridge_node.py`: 主要的桥接节点实现
- `config/mavros_apm.yaml`: ArduPilot专用MAVROS配置
- `launch/mavros_bridge.launch.py`: Launch文件

## 许可证

MIT License
