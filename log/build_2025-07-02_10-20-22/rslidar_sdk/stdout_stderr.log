[0m=============================================================[0m
[0m-- POINT_TYPE is XYZI[0m
[0m=============================================================[0m
[0m=============================================================[0m
[0m-- ROS Not Found. ROS Support is turned Off.[0m
[0m=============================================================[0m
Traceback (most recent call last):
  File "/opt/ros/humble/share/ament_cmake_core/cmake/package_templates/templates_2_cmake.py", line 21, in <module>
    from ament_package.templates import get_environment_hook_template_path
ModuleNotFoundError: No module named 'ament_package'
[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_package_templates-extras.cmake:41 (message):
  execute_process(/usr/bin/python3
  /opt/ros/humble/share/ament_cmake_core/cmake/package_templates/templates_2_cmake.py
  /home/<USER>/ros_ws/build/rslidar_sdk/ament_cmake_package_templates/templates.cmake)
  returned error code 1
Call Stack (most recent call first):
  /opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake:41 (include)
  /opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_include_directories-extras.cmake:8 (find_package)
  /opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake:41 (include)
  CMakeLists.txt:130 (find_package)

[0m
-- Configuring incomplete, errors occurred!
See also "/home/<USER>/ros_ws/build/rslidar_sdk/CMakeFiles/CMakeOutput.log".
