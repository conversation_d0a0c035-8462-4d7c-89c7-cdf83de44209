// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from rslidar_msg:msg/RslidarPacket.idl
// generated code does not contain a copyright notice

#ifndef RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_H_
#define RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "rslidar_msg/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_rslidar_msg
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  rslidar_msg,
  msg,
  RslidarPacket
)();

#ifdef __cplusplus
}
#endif

#endif  // RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_H_
