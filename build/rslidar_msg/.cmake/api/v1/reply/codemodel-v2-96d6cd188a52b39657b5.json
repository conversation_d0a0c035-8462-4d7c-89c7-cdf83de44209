{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-ecb1ce98d55a46bf3051.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"build": "rslidar_msg__py", "jsonFile": "directory-rslidar_msg__py-b6b9ba8c396c1639784e.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 0, "projectIndex": 0, "source": "/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py", "targetIndexes": [4]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "rslidar_msg", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_rslidar_msg_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_rslidar_msg_egg-2d5c96e462c6cbdc812d.json", "name": "ament_cmake_python_build_rslidar_msg_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_copy_rslidar_msg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_copy_rslidar_msg-4518bb38a1bbff19d372.json", "name": "ament_cmake_python_copy_rslidar_msg", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg-0ab45eb43e5b61bd03d0.json", "name": "rslidar_msg", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__cpp-7e62bb369cf84dd4be03.json", "name": "rslidar_msg__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "rslidar_msg__py::@c80b5731effdf274c015", "jsonFile": "target-rslidar_msg__py-b4d157a84701755e41be.json", "name": "rslidar_msg__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_generator_c-1b6a046a18b9e2bf28f3.json", "name": "rslidar_msg__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_generator_py-f488ac6db82d0248d13c.json", "name": "rslidar_msg__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_c-b266d7e277d8c6eb3200.json", "name": "rslidar_msg__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_c__pyext-80304295fad97811063a.json", "name": "rslidar_msg__rosidl_typesupport_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_cpp-06faa306d1e2ec0c42cf.json", "name": "rslidar_msg__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_c-2cc7c8bcd078b9f26264.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_c__pyext-c5b6fb2a78e78e0f2e57.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_cpp-d1b2a14683e4f9a7555e.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_c-846c079e44a8a4efae9a.json", "name": "rslidar_msg__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_c__pyext-e650405d2764e266a7aa.json", "name": "rslidar_msg__rosidl_typesupport_introspection_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_cpp-f47afece37f1ac1b5966.json", "name": "rslidar_msg__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg_uninstall-5d474d8b27d082eaf5cc.json", "name": "rslidar_msg_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-008531f139dac3a9d628.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros_ws/build/rslidar_msg", "source": "/home/<USER>/ros_ws/src/rslidar_msg"}, "version": {"major": 2, "minor": 3}}