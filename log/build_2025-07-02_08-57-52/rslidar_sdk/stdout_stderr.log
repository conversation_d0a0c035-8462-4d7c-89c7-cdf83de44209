-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
[0m=============================================================[0m
[0m-- POINT_TYPE is XYZI[0m
[0m=============================================================[0m
[0m=============================================================[0m
[0m-- ROS Not Found. ROS Support is turned Off.[0m
[0m=============================================================[0m
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
[0m=============================================================[0m
[0m-- ROS2 Found. ROS2 Support is turned On.[0m
[0m=============================================================[0m
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)
[0m=============================================================[0m
[0m-- CMake run for UNIX GNU Compiler[0m
[0m=============================================================[0m
[0m=============================================================[0m
[0m-- rs_driver Version : v1.5.17[0m
[0m=============================================================[0m
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk
[ 33%] [32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.o[0m
[100%] [32m[1mLinking CXX executable rslidar_sdk_node[0m
[100%] Built target rslidar_sdk_node
-- Install configuration: "Release"
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node" to ""
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
