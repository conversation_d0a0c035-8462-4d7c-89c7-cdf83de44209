[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_c[0m
[  3%] Built target rslidar_msg__cpp
[ 12%] Built target rslidar_msg__rosidl_generator_c
[ 12%] Built target ament_cmake_python_copy_rslidar_msg
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c[0m
[ 22%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c
[ 51%] Built target rslidar_msg__rosidl_typesupport_introspection_c
[ 51%] Built target rslidar_msg__rosidl_typesupport_c
[ 51%] Built target rslidar_msg__rosidl_typesupport_cpp
[ 61%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp
[ 70%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp
[ 70%] Built target rslidar_msg
[ 74%] Built target rslidar_msg__py
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_py[0m
[ 80%] Built target rslidar_msg__rosidl_generator_py
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c__pyext[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c__pyext[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext[0m
[ 87%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext
[100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext
[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext
running egg_info
writing rslidar_msg.egg-info/PKG-INFO
writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
writing top-level names to rslidar_msg.egg-info/top_level.txt
reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[100%] Built target ament_cmake_python_build_rslidar_msg_egg
