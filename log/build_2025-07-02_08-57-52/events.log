[0.000000] (-) TimerEvent: {}
[0.000120] (rslidar_msg) JobQueued: {'identifier': 'rslidar_msg', 'dependencies': OrderedDict()}
[0.000148] (rslidar_sdk) JobQueued: {'identifier': 'rslidar_sdk', 'dependencies': OrderedDict([('rslidar_msg', '/home/<USER>/ros_ws/install/rslidar_msg')])}
[0.000171] (rslidar_msg) JobStarted: {'identifier': 'rslidar_msg'}
[0.004322] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'cmake'}
[0.004812] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_msg', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.048623] (rslidar_msg) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.085351] (rslidar_msg) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.089549] (rslidar_msg) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.099708] (-) TimerEvent: {}
[0.136447] (rslidar_msg) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.140458] (rslidar_msg) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.140685] (rslidar_msg) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.140805] (rslidar_msg) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.142516] (rslidar_msg) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.193289] (rslidar_msg) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.197401] (rslidar_msg) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.197460] (rslidar_msg) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.197696] (rslidar_msg) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.199752] (-) TimerEvent: {}
[0.202187] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.298528] (rslidar_msg) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.299856] (-) TimerEvent: {}
[0.353613] (rslidar_msg) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.375719] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.378824] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.384268] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.392212] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.400072] (-) TimerEvent: {}
[0.402305] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.419267] (rslidar_msg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.443361] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.444989] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.500185] (-) TimerEvent: {}
[0.504984] (rslidar_msg) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.522673] (rslidar_msg) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.553072] (rslidar_msg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.559593] (rslidar_msg) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[0.600330] (-) TimerEvent: {}
[0.606737] (rslidar_msg) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[0.607006] (rslidar_msg) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[0.655515] (rslidar_msg) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[0.656538] (rslidar_msg) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[0.686903] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.700506] (-) TimerEvent: {}
[0.800835] (-) TimerEvent: {}
[0.843481] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.901072] (-) TimerEvent: {}
[1.001455] (-) TimerEvent: {}
[1.064541] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.101716] (-) TimerEvent: {}
[1.149028] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.164081] (rslidar_msg) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[1.165905] (rslidar_msg) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.181372] (rslidar_msg) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[1.181533] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.181600] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.181658] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[1.193957] (rslidar_msg) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[1.201939] (-) TimerEvent: {}
[1.221361] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.234173] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.237025] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.238616] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.239843] (rslidar_msg) StdoutLine: {'line': b'-- Configuring done\n'}
[1.254389] (rslidar_msg) StdoutLine: {'line': b'-- Generating done\n'}
[1.260170] (rslidar_msg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg\n'}
[1.265774] (rslidar_msg) CommandEnded: {'returncode': 0}
[1.266417] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'build'}
[1.267186] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_msg', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.298450] (rslidar_msg) StdoutLine: {'line': b'[  3%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[1.302059] (-) TimerEvent: {}
[1.305842] (rslidar_msg) StdoutLine: {'line': b'[  6%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[1.311489] (rslidar_msg) StdoutLine: {'line': b'[  6%] Built target ament_cmake_python_copy_rslidar_msg\n'}
[1.402273] (-) TimerEvent: {}
[1.435356] (rslidar_msg) StdoutLine: {'line': b'running egg_info\n'}
[1.435752] (rslidar_msg) StdoutLine: {'line': b'creating rslidar_msg.egg-info\n'}
[1.435905] (rslidar_msg) StdoutLine: {'line': b'writing rslidar_msg.egg-info/PKG-INFO\n'}
[1.436072] (rslidar_msg) StdoutLine: {'line': b'writing dependency_links to rslidar_msg.egg-info/dependency_links.txt\n'}
[1.436256] (rslidar_msg) StdoutLine: {'line': b'writing top-level names to rslidar_msg.egg-info/top_level.txt\n'}
[1.436430] (rslidar_msg) StdoutLine: {'line': b"writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.438569] (rslidar_msg) StdoutLine: {'line': b"reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.439125] (rslidar_msg) StdoutLine: {'line': b"writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.449603] (rslidar_msg) StdoutLine: {'line': b'[  6%] Built target rslidar_msg__cpp\n'}
[1.455123] (rslidar_msg) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_c.dir/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.c.o\x1b[0m\n'}
[1.457100] (rslidar_msg) StdoutLine: {'line': b'[ 12%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[1.457460] (rslidar_msg) StdoutLine: {'line': b'[ 19%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[1.457551] (rslidar_msg) StdoutLine: {'line': b'[ 19%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[1.464812] (rslidar_msg) StdoutLine: {'line': b'[ 19%] Built target ament_cmake_python_build_rslidar_msg_egg\n'}
[1.499086] (rslidar_msg) StdoutLine: {'line': b'[ 22%] \x1b[32m\x1b[1mLinking C shared library librslidar_msg__rosidl_generator_c.so\x1b[0m\n'}
[1.502391] (-) TimerEvent: {}
[1.527738] (rslidar_msg) StdoutLine: {'line': b'[ 22%] Built target rslidar_msg__rosidl_generator_c\n'}
[1.536952] (rslidar_msg) StdoutLine: {'line': b'[ 32%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[1.537124] (rslidar_msg) StdoutLine: {'line': b'[ 32%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[1.537198] (rslidar_msg) StdoutLine: {'line': b'[ 32%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[1.594238] (rslidar_msg) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/rslidar_msg/msg/rslidar_packet__type_support.cpp.o\x1b[0m\n'}
[1.599792] (rslidar_msg) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/rslidar_msg/msg/detail/dds_fastrtps/rslidar_packet__type_support.cpp.o\x1b[0m\n'}
[1.602483] (-) TimerEvent: {}
[1.603482] (rslidar_msg) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp.o\x1b[0m\n'}
[1.688714] (rslidar_msg) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/rslidar_msg/msg/detail/rslidar_packet__type_support.c.o\x1b[0m\n'}
[1.702589] (-) TimerEvent: {}
[1.712349] (rslidar_msg) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_c.dir/rosidl_typesupport_c/rslidar_msg/msg/rslidar_packet__type_support.cpp.o\x1b[0m\n'}
[1.718425] (rslidar_msg) StdoutLine: {'line': b'[ 51%] \x1b[32m\x1b[1mLinking C shared library librslidar_msg__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[1.740349] (rslidar_msg) StdoutLine: {'line': b'[ 54%] \x1b[32m\x1b[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_c.so\x1b[0m\n'}
[1.746981] (rslidar_msg) StdoutLine: {'line': b'[ 54%] Built target rslidar_msg__rosidl_typesupport_introspection_c\n'}
[1.749415] (rslidar_msg) StdoutLine: {'line': b'[ 58%] \x1b[32m\x1b[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_cpp.so\x1b[0m\n'}
[1.775559] (rslidar_msg) StdoutLine: {'line': b'[ 58%] Built target rslidar_msg__rosidl_typesupport_c\n'}
[1.788303] (rslidar_msg) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/rslidar_msg/msg/detail/rslidar_packet__type_support_c.cpp.o\x1b[0m\n'}
[1.788496] (rslidar_msg) StdoutLine: {'line': b'[ 61%] Built target rslidar_msg__rosidl_typesupport_cpp\n'}
[1.802682] (-) TimerEvent: {}
[1.840617] (rslidar_msg) StdoutLine: {'line': b'[ 64%] \x1b[32m\x1b[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[1.865052] (rslidar_msg) StdoutLine: {'line': b'[ 67%] \x1b[32m\x1b[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[1.891739] (rslidar_msg) StdoutLine: {'line': b'[ 67%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp\n'}
[1.902815] (-) TimerEvent: {}
[1.918948] (rslidar_msg) StdoutLine: {'line': b'[ 67%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp\n'}
[1.961838] (rslidar_msg) StdoutLine: {'line': b'[ 70%] \x1b[32m\x1b[1mLinking CXX shared library librslidar_msg__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[2.003045] (-) TimerEvent: {}
[2.007367] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c\n'}
[2.020828] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg\n'}
[2.035671] (rslidar_msg) StdoutLine: {'line': b'[ 74%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[2.103241] (-) TimerEvent: {}
[2.195494] (rslidar_msg) StdoutLine: {'line': b'[ 74%] Built target rslidar_msg__py\n'}
[2.203434] (-) TimerEvent: {}
[2.208295] (rslidar_msg) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_py.dir/rosidl_generator_py/rslidar_msg/msg/_rslidar_packet_s.c.o\x1b[0m\n'}
[2.277617] (rslidar_msg) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/rslidar_msg/librslidar_msg__rosidl_generator_py.so\x1b[0m\n'}
[2.303626] (-) TimerEvent: {}
[2.303890] (rslidar_msg) StdoutLine: {'line': b'[ 80%] Built target rslidar_msg__rosidl_generator_py\n'}
[2.319614] (rslidar_msg) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[2.319799] (rslidar_msg) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[2.320416] (rslidar_msg) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/rslidar_msg__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[2.379255] (rslidar_msg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[2.379447] (rslidar_msg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[2.379536] (rslidar_msg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[2.403729] (-) TimerEvent: {}
[2.411520] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext\n'}
[2.411714] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\n'}
[2.411979] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext\n'}
[2.419521] (rslidar_msg) CommandEnded: {'returncode': 0}
[2.420101] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'install'}
[2.426257] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[2.432550] (rslidar_msg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[2.432716] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg\n'}
[2.432847] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.432941] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.433021] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h\n'}
[2.433109] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.433173] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h\n'}
[2.433267] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h\n'}
[2.433357] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h\n'}
[2.433420] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.c\n'}
[2.433489] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h\n'}
[2.433618] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh\n'}
[2.433704] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv\n'}
[2.433812] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so\n'}
[2.433898] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so" to ""\n'}
[2.433961] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.434063] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.434146] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.434223] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h\n'}
[2.434307] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[2.434370] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so\n'}
[2.434434] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[2.434501] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.434565] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.434768] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[2.434829] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp\n'}
[2.434928] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.435017] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp\n'}
[2.435104] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp\n'}
[2.435177] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp\n'}
[2.435256] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp\n'}
[2.435345] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.435425] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.435560] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[2.435631] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.435666] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/dds_fastrtps\n'}
[2.435692] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[2.435722] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so\n'}
[2.435744] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[2.435767] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.435788] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.435812] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[2.435833] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.435853] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h\n'}
[2.435873] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.c\n'}
[2.435893] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so\n'}
[2.435912] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so" to ""\n'}
[2.435932] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so\n'}
[2.435952] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so" to ""\n'}
[2.435971] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg\n'}
[2.435992] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg\n'}
[2.436012] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail\n'}
[2.436033] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp\n'}
[2.436054] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp\n'}
[2.436075] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so\n'}
[2.436096] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[2.436116] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so\n'}
[2.436137] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so" to ""\n'}
[2.436157] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh\n'}
[2.436177] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv\n'}
[2.436197] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info\n'}
[2.436218] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[2.436238] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt\n'}
[2.436258] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[2.436278] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[2.436299] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg\n'}
[2.436319] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py\n'}
[2.436339] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c\n'}
[2.436358] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[2.436380] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c\n'}
[2.436400] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.436420] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so\n'}
[2.436441] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg\n'}
[2.436461] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py\n'}
[2.436481] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py\n'}
[2.436506] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c\n'}
[2.436527] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.436548] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.458940] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...\n"}
[2.459052] (rslidar_msg) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...\n"}
[2.459083] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...\n"}
[2.459108] (rslidar_msg) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py'...\n"}
[2.459133] (rslidar_msg) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py'...\n"}
[2.461765] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.461867] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[2.461929] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.462030] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[2.462058] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[2.462152] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[2.462178] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so\n'}
[2.462214] (rslidar_msg) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so" to ""\n'}
[2.462257] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl\n'}
[2.462281] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg\n'}
[2.462310] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg\n'}
[2.462341] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg\n'}
[2.462386] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh\n'}
[2.462426] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv\n'}
[2.462466] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh\n'}
[2.462516] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv\n'}
[2.462556] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash\n'}
[2.462594] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh\n'}
[2.462636] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh\n'}
[2.462677] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv\n'}
[2.462715] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv\n'}
[2.462765] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg\n'}
[2.462824] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake\n'}
[2.462870] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake\n'}
[2.462918] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[2.462962] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[2.463008] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake\n'}
[2.463054] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[2.463098] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[2.463145] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake\n'}
[2.463189] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[2.463234] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake\n'}
[2.463276] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake\n'}
[2.463324] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake\n'}
[2.463366] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[2.463412] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake\n'}
[2.463454] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[2.463503] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake\n'}
[2.463552] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake\n'}
[2.463594] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake\n'}
[2.463632] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[2.463674] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[2.463716] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[2.463759] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake\n'}
[2.463798] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[2.463841] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[2.463881] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake\n'}
[2.463916] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake\n'}
[2.463955] (rslidar_msg) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml\n'}
[2.465405] (rslidar_msg) CommandEnded: {'returncode': 0}
[2.476066] (rslidar_msg) JobEnded: {'identifier': 'rslidar_msg', 'rc': 0}
[2.476393] (rslidar_sdk) JobStarted: {'identifier': 'rslidar_sdk'}
[2.480798] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'cmake'}
[2.481302] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_sdk', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[2.503813] (-) TimerEvent: {}
[2.519151] (rslidar_sdk) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[2.553773] (rslidar_sdk) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[2.558077] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[2.603929] (-) TimerEvent: {}
[2.604230] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[2.609605] (rslidar_sdk) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[2.609712] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[2.609879] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[2.611601] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[2.660823] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[2.666039] (rslidar_sdk) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[2.666180] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[2.666379] (rslidar_sdk) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[2.666730] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[2.666831] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- POINT_TYPE is XYZI\x1b[0m\n'}
[2.666894] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[2.671086] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[2.671172] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS Not Found. ROS Support is turned Off.\x1b[0m\n'}
[2.671233] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[2.704127] (-) TimerEvent: {}
[2.757359] (rslidar_sdk) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[2.804345] (-) TimerEvent: {}
[2.823759] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[2.825833] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[2.829786] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[2.835678] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[2.843425] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.864344] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[2.865510] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[2.904494] (-) TimerEvent: {}
[2.918053] (rslidar_sdk) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[2.931459] (rslidar_sdk) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[2.951454] (rslidar_sdk) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[2.955170] (rslidar_sdk) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[3.000408] (rslidar_sdk) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[3.000697] (rslidar_sdk) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[3.004555] (-) TimerEvent: {}
[3.048718] (rslidar_sdk) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[3.049403] (rslidar_sdk) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[3.075286] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.075383] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS2 Found. ROS2 Support is turned On.\x1b[0m\n'}
[3.075446] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.076156] (rslidar_sdk) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[3.094088] (rslidar_sdk) StdoutLine: {'line': b'-- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)\n'}
[3.102815] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.102958] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- CMake run for UNIX GNU Compiler\x1b[0m\n'}
[3.103103] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.103173] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.103230] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- rs_driver Version : v1.5.17\x1b[0m\n'}
[3.103283] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[3.104654] (-) TimerEvent: {}
[3.142196] (rslidar_sdk) StdoutLine: {'line': b'-- Configuring done\n'}
[3.147596] (rslidar_sdk) StdoutLine: {'line': b'-- Generating done\n'}
[3.149621] (rslidar_sdk) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk\n'}
[3.155270] (rslidar_sdk) CommandEnded: {'returncode': 0}
[3.156453] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'build'}
[3.156469] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_sdk', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[3.192491] (rslidar_sdk) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.o\x1b[0m\n'}
[3.193729] (rslidar_sdk) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.o\x1b[0m\n'}
[3.204743] (-) TimerEvent: {}
[3.305045] (-) TimerEvent: {}
[3.405383] (-) TimerEvent: {}
[3.505741] (-) TimerEvent: {}
[3.606084] (-) TimerEvent: {}
[3.706433] (-) TimerEvent: {}
[3.806782] (-) TimerEvent: {}
[3.907068] (-) TimerEvent: {}
[4.007406] (-) TimerEvent: {}
[4.107768] (-) TimerEvent: {}
[4.208120] (-) TimerEvent: {}
[4.308493] (-) TimerEvent: {}
[4.408766] (-) TimerEvent: {}
[4.509137] (-) TimerEvent: {}
[4.609494] (-) TimerEvent: {}
[4.709737] (-) TimerEvent: {}
[4.810014] (-) TimerEvent: {}
[4.910373] (-) TimerEvent: {}
[5.010640] (-) TimerEvent: {}
[5.110986] (-) TimerEvent: {}
[5.211318] (-) TimerEvent: {}
[5.311669] (-) TimerEvent: {}
[5.412017] (-) TimerEvent: {}
[5.512357] (-) TimerEvent: {}
[5.612716] (-) TimerEvent: {}
[5.713055] (-) TimerEvent: {}
[5.813392] (-) TimerEvent: {}
[5.913756] (-) TimerEvent: {}
[6.014104] (-) TimerEvent: {}
[6.114469] (-) TimerEvent: {}
[6.214834] (-) TimerEvent: {}
[6.315189] (-) TimerEvent: {}
[6.415547] (-) TimerEvent: {}
[6.515896] (-) TimerEvent: {}
[6.616178] (-) TimerEvent: {}
[6.716564] (-) TimerEvent: {}
[6.816858] (-) TimerEvent: {}
[6.917194] (-) TimerEvent: {}
[7.017561] (-) TimerEvent: {}
[7.117904] (-) TimerEvent: {}
[7.218260] (-) TimerEvent: {}
[7.318610] (-) TimerEvent: {}
[7.418850] (-) TimerEvent: {}
[7.519176] (-) TimerEvent: {}
[7.619544] (-) TimerEvent: {}
[7.719900] (-) TimerEvent: {}
[7.820243] (-) TimerEvent: {}
[7.920622] (-) TimerEvent: {}
[8.020977] (-) TimerEvent: {}
[8.121314] (-) TimerEvent: {}
[8.221662] (-) TimerEvent: {}
[8.321998] (-) TimerEvent: {}
[8.422334] (-) TimerEvent: {}
[8.522642] (-) TimerEvent: {}
[8.623006] (-) TimerEvent: {}
[8.723365] (-) TimerEvent: {}
[8.823640] (-) TimerEvent: {}
[8.923965] (-) TimerEvent: {}
[9.024299] (-) TimerEvent: {}
[9.124657] (-) TimerEvent: {}
[9.225000] (-) TimerEvent: {}
[9.325337] (-) TimerEvent: {}
[9.425651] (-) TimerEvent: {}
[9.526001] (-) TimerEvent: {}
[9.626334] (-) TimerEvent: {}
[9.726726] (-) TimerEvent: {}
[9.827062] (-) TimerEvent: {}
[9.927407] (-) TimerEvent: {}
[10.027777] (-) TimerEvent: {}
[10.128115] (-) TimerEvent: {}
[10.228445] (-) TimerEvent: {}
[10.328792] (-) TimerEvent: {}
[10.429134] (-) TimerEvent: {}
[10.529465] (-) TimerEvent: {}
[10.629704] (-) TimerEvent: {}
[10.730022] (-) TimerEvent: {}
[10.830354] (-) TimerEvent: {}
[10.930715] (-) TimerEvent: {}
[11.031055] (-) TimerEvent: {}
[11.131399] (-) TimerEvent: {}
[11.231762] (-) TimerEvent: {}
[11.332121] (-) TimerEvent: {}
[11.432464] (-) TimerEvent: {}
[11.532822] (-) TimerEvent: {}
[11.633159] (-) TimerEvent: {}
[11.733498] (-) TimerEvent: {}
[11.833828] (-) TimerEvent: {}
[11.934165] (-) TimerEvent: {}
[12.034539] (-) TimerEvent: {}
[12.134869] (-) TimerEvent: {}
[12.235216] (-) TimerEvent: {}
[12.335547] (-) TimerEvent: {}
[12.435886] (-) TimerEvent: {}
[12.536219] (-) TimerEvent: {}
[12.636559] (-) TimerEvent: {}
[12.736902] (-) TimerEvent: {}
[12.837237] (-) TimerEvent: {}
[12.937589] (-) TimerEvent: {}
[13.037924] (-) TimerEvent: {}
[13.138283] (-) TimerEvent: {}
[13.238606] (-) TimerEvent: {}
[13.338944] (-) TimerEvent: {}
[13.439271] (-) TimerEvent: {}
[13.539616] (-) TimerEvent: {}
[13.639952] (-) TimerEvent: {}
[13.740287] (-) TimerEvent: {}
[13.840628] (-) TimerEvent: {}
[13.940977] (-) TimerEvent: {}
[14.041305] (-) TimerEvent: {}
[14.141652] (-) TimerEvent: {}
[14.241986] (-) TimerEvent: {}
[14.342338] (-) TimerEvent: {}
[14.407258] (rslidar_sdk) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable rslidar_sdk_node\x1b[0m\n'}
[14.442571] (-) TimerEvent: {}
[14.542888] (-) TimerEvent: {}
[14.543616] (rslidar_sdk) StdoutLine: {'line': b'[100%] Built target rslidar_sdk_node\n'}
[14.552502] (rslidar_sdk) CommandEnded: {'returncode': 0}
[14.553132] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'install'}
[14.553406] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 49400 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('SSH_TTY', '/dev/pts/0'), ('ROS_PYTHON_VERSION', '3'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '39'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 49400 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[14.559927] (rslidar_sdk) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[14.560058] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node\n'}
[14.560607] (rslidar_sdk) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node" to ""\n'}
[14.560870] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch\n'}
[14.561040] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py\n'}
[14.561097] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch\n'}
[14.561127] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py\n'}
[14.561155] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz\n'}
[14.561182] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz\n'}
[14.561221] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz\n'}
[14.561246] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk\n'}
[14.561285] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk\n'}
[14.561308] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh\n'}
[14.561331] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv\n'}
[14.561353] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh\n'}
[14.561374] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv\n'}
[14.561396] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash\n'}
[14.561417] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh\n'}
[14.561439] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh\n'}
[14.561461] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv\n'}
[14.561482] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv\n'}
[14.561506] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk\n'}
[14.561531] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake\n'}
[14.561554] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake\n'}
[14.561576] (rslidar_sdk) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml\n'}
[14.562384] (rslidar_sdk) CommandEnded: {'returncode': 0}
[14.570687] (rslidar_sdk) JobEnded: {'identifier': 'rslidar_sdk', 'rc': 0}
[14.570887] (-) EventReactorShutdown: {}
