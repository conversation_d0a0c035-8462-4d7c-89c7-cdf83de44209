{"artifacts": [{"path": "librslidar_msg__rosidl_typesupport_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "add_definitions", "find_package"], "files": ["/opt/ros/humble/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 90, "parent": 4}, {"command": 4, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 120, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 123, "parent": 4}, {"command": 6, "file": 0, "line": 103, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 6, "parent": 12}, {"command": 8, "file": 6, "line": 21, "parent": 13}, {"file": 5, "parent": 14}, {"command": 1, "file": 5, "line": 41, "parent": 15}, {"file": 4, "parent": 16}, {"command": 7, "file": 4, "line": 25, "parent": 17}, {"command": 6, "file": 0, "line": 100, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 11, "fragment": "-Wall"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 7, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_GENERATOR_C_BUILDING_DLL_rslidar_msg"}, {"backtrace": 18, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 7, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}], "language": "CXX", "languageStandard": {"backtraces": [19], "standard": "14"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "CXX"}, "name": "rslidar_msg__rosidl_typesupport_c", "nameOnDisk": "librslidar_msg__rosidl_typesupport_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_c/rslidar_msg/msg/rslidar_packet__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_c/rslidar_msg/msg/rslidar_packet__type_support.cpp.rule", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}