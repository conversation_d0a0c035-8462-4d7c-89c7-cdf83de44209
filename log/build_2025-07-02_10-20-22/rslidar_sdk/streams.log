[0.005s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.015s] [0m=============================================================[0m
[0.015s] [0m-- POINT_TYPE is XYZI[0m
[0.015s] [0m=============================================================[0m
[0.019s] [0m=============================================================[0m
[0.019s] [0m-- ROS Not Found. ROS Support is turned Off.[0m
[0.019s] [0m=============================================================[0m
[0.112s] Traceback (most recent call last):
[0.112s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/package_templates/templates_2_cmake.py", line 21, in <module>
[0.113s]     from ament_package.templates import get_environment_hook_template_path
[0.113s] ModuleNotFoundError: No module named 'ament_package'
[0.121s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_package_templates-extras.cmake:41 (message):
[0.121s]   execute_process(/usr/bin/python3
[0.121s]   /opt/ros/humble/share/ament_cmake_core/cmake/package_templates/templates_2_cmake.py
[0.121s]   /home/<USER>/ros_ws/build/rslidar_sdk/ament_cmake_package_templates/templates.cmake)
[0.121s]   returned error code 1
[0.121s] Call Stack (most recent call first):
[0.121s]   /opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake:41 (include)
[0.121s]   /opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_include_directories-extras.cmake:8 (find_package)
[0.121s]   /opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake:41 (include)
[0.121s]   CMakeLists.txt:130 (find_package)
[0.121s] 
[0.121s] [0m
[0.121s] -- Configuring incomplete, errors occurred!
[0.121s] See also "/home/<USER>/ros_ws/build/rslidar_sdk/CMakeFiles/CMakeOutput.log".
[0.123s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '1': /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
