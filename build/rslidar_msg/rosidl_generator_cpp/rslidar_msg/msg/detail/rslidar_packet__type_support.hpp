// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from rslidar_msg:msg/RslidarPacket.idl
// generated code does not contain a copyright notice

#ifndef RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_HPP_
#define RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_rslidar_msg
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  rslidar_msg,
  msg,
  RslidarPacket
)();
#ifdef __cplusplus
}
#endif

#endif  // RSLIDAR_MSG__MSG__DETAIL__RSLIDAR_PACKET__TYPE_SUPPORT_HPP_
